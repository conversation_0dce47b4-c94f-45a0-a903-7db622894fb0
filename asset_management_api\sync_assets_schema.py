#!/usr/bin/env python3
"""
Complete database schema synchronization for assets table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
from app.models.assets import AssetStatus, AssetCondition
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def sync_assets_schema():
    """Synchronize the assets table schema with the model."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            logger.info("🔍 Starting complete assets table schema synchronization...")

            # Step 1: Check current table structure
            result = conn.execute(text("DESCRIBE assets"))
            columns = result.fetchall()
            print("Current assets table columns:")
            column_names = []
            for col in columns:
                column_names.append(col[0])
                print(f"  {col[0]}: {col[1]} {'NULL' if col[2] == 'YES' else 'NOT NULL'} {f'DEFAULT {col[4]}' if col[4] else ''}")
            
            # Step 2: Fix the name column if it exists
            if 'name' in column_names:
                logger.info("❗ Found unexpected 'name' column - making it nullable")
                conn.execute(text("ALTER TABLE assets MODIFY COLUMN name VARCHAR(255) NULL DEFAULT NULL"))
                logger.info("✅ Made 'name' column nullable")
            
            # Step 3: Update enum columns to match model
            logger.info("🔧 Updating enum columns...")
            
            # Get enum values from the model
            status_values = "','".join([status.value for status in AssetStatus])
            condition_values = "','".join([condition.value for condition in AssetCondition])
            
            # Update status column
            status_enum = f"ENUM('{status_values}')"
            conn.execute(text(f"ALTER TABLE assets MODIFY COLUMN status {status_enum} NOT NULL DEFAULT 'New'"))
            logger.info("✅ Updated status column with correct enum values")
            
            # Update condition column  
            condition_enum = f"ENUM('{condition_values}')"
            conn.execute(text(f"ALTER TABLE assets MODIFY COLUMN `condition` {condition_enum} NOT NULL DEFAULT 'Good'"))
            logger.info("✅ Updated condition column with correct enum values")
            
            # Step 4: Ensure asset_id column is properly indexed and unique
            try:
                # Check if unique constraint exists
                result = conn.execute(text("SHOW INDEX FROM assets WHERE Column_name = 'asset_id'"))
                indexes = result.fetchall()
                
                has_unique = any('UNIQUE' in str(idx) for idx in indexes)
                if not has_unique:
                    logger.info("🔧 Adding unique constraint to asset_id column...")
                    conn.execute(text("ALTER TABLE assets ADD UNIQUE INDEX unique_asset_id (asset_id)"))
                    logger.info("✅ Added unique constraint to asset_id")
                
            except Exception as e:
                logger.warning(f"⚠️  Could not add unique constraint (may already exist): {e}")
            
            # Commit all changes
            conn.commit()
            logger.info("🎉 Assets table schema synchronization completed successfully!")
            
            # Step 5: Verify the final schema
            logger.info("📊 Final schema verification:")
            result = conn.execute(text("DESCRIBE assets"))
            columns = result.fetchall()
            for col in columns:
                print(f"  ✓ {col[0]}: {col[1]} {'NULL' if col[2] == 'YES' else 'NOT NULL'} {f'DEFAULT {col[4]}' if col[4] else ''}")
                
    except Exception as e:
        logger.error(f"❌ Error during schema synchronization: {e}")
        raise

if __name__ == "__main__":
    sync_assets_schema()
