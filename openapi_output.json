{"openapi": "3.1.0", "info": {"title": "RFI Asset Management API", "description": "Asset Management System API - Converted from Node.js to Python FastAPI", "version": "1.0.0"}, "paths": {"/{full_path}": {"options": {"summary": "Options Handler", "description": "Handle CORS preflight requests.", "operationId": "options_handler__full_path__options", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"tags": ["Health"], "summary": "Health Check", "description": "Health check endpoint.", "operationId": "health_check_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/": {"get": {"tags": ["Root"], "summary": "Root", "description": "Root endpoint.", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/auth/login": {"post": {"tags": ["Authentication"], "summary": "Login User", "description": "Login user with email/username/loginId and password.\nFixed to match frontend expectations.", "operationId": "login_user_api_auth_login_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Login Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/auth/register": {"post": {"tags": ["Authentication"], "summary": "Register User", "description": "Register a new user. Frontend expects this endpoint.\nRequires admin access.", "operationId": "register_user_api_auth_register_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "User Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/auth/profile": {"get": {"tags": ["Authentication"], "summary": "Get User Profile", "description": "Get current user profile.", "operationId": "get_user_profile_api_auth_profile_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "put": {"tags": ["Authentication"], "summary": "Update User Profile", "description": "Update current user profile.", "operationId": "update_user_profile_api_auth_profile_put", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Profile Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/auth/change-password": {"post": {"tags": ["Authentication"], "summary": "Change Password", "description": "Change user password.", "operationId": "change_password_api_auth_change_password_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Password Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/auth/logout": {"post": {"tags": ["Authentication"], "summary": "Logout User", "description": "Logout user (token invalidation would be handled on client side).", "operationId": "logout_user_api_auth_logout_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/auth/verify-token": {"get": {"tags": ["Authentication"], "summary": "Verify <PERSON>", "description": "Verify if token is valid and return user info.", "operationId": "verify_token_api_auth_verify_token_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/auth/health": {"get": {"tags": ["Authentication"], "summary": "Auth Health Check", "description": "Auth system health check.", "operationId": "auth_health_check_api_auth_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/auth/users": {"get": {"tags": ["Authentication"], "summary": "Get All Users", "description": "Get all users with optional filtering.\nFrontend expects this endpoint for user management.", "operationId": "get_all_users_api_auth_users_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string", "title": "Search"}}, {"name": "user_group", "in": "query", "required": false, "schema": {"type": "string", "title": "User Group"}}, {"name": "status", "in": "query", "required": false, "schema": {"type": "boolean", "title": "Status"}}, {"name": "access_level", "in": "query", "required": false, "schema": {"type": "string", "title": "Access Level"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/auth/users/{user_id}": {"put": {"tags": ["Authentication"], "summary": "Update User", "description": "Update a user. Frontend expects this endpoint.\nUsers can update themselves, admins can update anyone.", "operationId": "update_user_api_auth_users__user_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "User Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/vendors/": {"get": {"tags": ["Vend<PERSON>"], "summary": "Get Vendors", "description": "Get all vendors with optional search and pagination.\nEquivalent to GET / in Node.js version.", "operationId": "get_vendors_api_vendors__get", "parameters": [{"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Vendors Api Vendors  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Vend<PERSON>"], "summary": "Create <PERSON><PERSON><PERSON>", "description": "Create new vendor.\nEquivalent to POST / in Node.js version.", "operationId": "create_vendor_api_vendors__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendorCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Vendor Api Vendors  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/vendors/{vendor_id}": {"get": {"tags": ["Vend<PERSON>"], "summary": "<PERSON>or", "description": "Get vendor by ID.\nEquivalent to GET /:id in Node.js version.", "operationId": "get_vendor_api_vendors__vendor_id__get", "parameters": [{"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Vendor Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Vendor Api Vendors  Vendor Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Vend<PERSON>"], "summary": "Update Vendor", "description": "Update vendor.\nEquivalent to PUT /:id in Node.js version.", "operationId": "update_vendor_api_vendors__vendor_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Vendor Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VendorUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Vendor Api Vendors  Vendor Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Vend<PERSON>"], "summary": "Delete Vendor", "description": "Delete vendor.\nEquivalent to DELETE /:id in Node.js version.", "operationId": "delete_vendor_api_vendors__vendor_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Vendor Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/vendors/{vendor_id}/secondary-contacts": {"put": {"tags": ["Vend<PERSON>"], "summary": "Update Secondary Contacts", "description": "Update secondary contacts.\nEquivalent to PUT /:id/secondary-contacts in Node.js version.", "operationId": "update_secondary_contacts_api_vendors__vendor_id__secondary_contacts_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Vendor Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Contacts Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Secondary Contacts Api Vendors  Vendor Id  Secondary Contacts Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Vend<PERSON>"], "summary": "Get Secondary Contacts", "description": "Get secondary contacts.\nEquivalent to GET /:id/secondary-contacts in Node.js version.", "operationId": "get_secondary_contacts_api_vendors__vendor_id__secondary_contacts_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Vendor Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Secondary Contacts Api Vendors  Vendor Id  Secondary Contacts Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/vendors/{vendor_id}/addresses": {"put": {"tags": ["Vend<PERSON>"], "summary": "Update Addresses", "description": "Update addresses.\nEquivalent to PUT /:id/addresses in Node.js version.", "operationId": "update_addresses_api_vendors__vendor_id__addresses_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Vendor Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Addresses Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Addresses Api Vendors  Vendor Id  Addresses Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Vend<PERSON>"], "summary": "Get Addresses", "description": "Get addresses.\nEquivalent to GET /:id/addresses in Node.js version.", "operationId": "get_addresses_api_vendors__vendor_id__addresses_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "vendor_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Vendor Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Addresses Api Vendors  Vendor Id  Addresses Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/transaction-orders/": {"get": {"tags": ["Transaction Orders"], "summary": "Get Transaction Orders", "description": "Get all transaction orders with filtering and pagination.\nEquivalent to GET / in Node.js version.", "operationId": "get_transaction_orders_api_transaction_orders__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "dateFrom", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Datefrom"}}, {"name": "dateTo", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateto"}}, {"name": "vendor", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "consumable", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Consumable"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Transaction Orders Api Transaction Orders  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Transaction Orders"], "summary": "Create Transaction Order", "description": "Create new transaction order.\nEquivalent to POST / in Node.js version.", "operationId": "create_transaction_order_api_transaction_orders__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionOrderCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Transaction Order Api Transaction Orders  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Transaction Orders"], "summary": "Bulk Delete Transaction Orders", "description": "Bulk delete transaction orders.\nEquivalent to DELETE / in Node.js version.", "operationId": "bulk_delete_transaction_orders_api_transaction_orders__delete", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Delete Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/transaction-orders/summary": {"get": {"tags": ["Transaction Orders"], "summary": "Get Order Summary", "description": "Get order summary statistics.\nEquivalent to GET /summary in Node.js version.", "operationId": "get_order_summary_api_transaction_orders_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Order Summary Api Transaction Orders Summary Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/transaction-orders/consumable-summary": {"get": {"tags": ["Transaction Orders"], "summary": "Get Consumable Summary", "description": "Get consumable summary statistics.\nEquivalent to GET /consumable-summary in Node.js version.", "operationId": "get_consumable_summary_api_transaction_orders_consumable_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "consumable", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Consumable"}}, {"name": "dateFrom", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Datefrom"}}, {"name": "dateTo", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateto"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Consumable Summary Api Transaction Orders Consumable Summary Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/transaction-orders/stock-summary": {"get": {"tags": ["Transaction Orders"], "summary": "Get Stock Summary", "description": "Get stock summary by location and item.\nEquivalent to GET /stock-summary in Node.js version.", "operationId": "get_stock_summary_api_transaction_orders_stock_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "item", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, {"name": "consumable", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Consumable"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Stock Summary Api Transaction Orders Stock Summary Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/transaction-orders/{order_id}": {"get": {"tags": ["Transaction Orders"], "summary": "Get Transaction Order", "description": "Get single transaction order by ID.\nEquivalent to GET /:id in Node.js version.", "operationId": "get_transaction_order_api_transaction_orders__order_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Transaction Order Api Transaction Orders  Order Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Transaction Orders"], "summary": "Update Transaction Order", "description": "Update transaction order.\nEquivalent to PUT /:id in Node.js version.", "operationId": "update_transaction_order_api_transaction_orders__order_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionOrderUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Transaction Order Api Transaction Orders  Order Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Transaction Orders"], "summary": "Delete Transaction Order", "description": "Delete transaction order.\nEquivalent to DELETE /:id in Node.js version.", "operationId": "delete_transaction_order_api_transaction_orders__order_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/transaction-orders/{order_id}/status": {"patch": {"tags": ["Transaction Orders"], "summary": "Update Order Status", "description": "Update order status.\nEquivalent to PATCH /:id/status in Node.js version.", "operationId": "update_order_status_api_transaction_orders__order_id__status_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "order_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Order Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransactionOrderStatusUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Order Status Api Transaction Orders  Order Id  Status Patch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/transaction-orders/config/consumable-categories": {"get": {"tags": ["Transaction Orders"], "summary": "Get Consumable Categories", "description": "Get consumable categories for item dropdown.", "operationId": "get_consumable_categories_api_transaction_orders_config_consumable_categories_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Consumable Categories Api Transaction Orders Config Consumable Categories Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/transaction-orders/config/packing-locations": {"get": {"tags": ["Transaction Orders"], "summary": "Get Packing Locations", "description": "Get packing locations for location dropdown.", "operationId": "get_packing_locations_api_transaction_orders_config_packing_locations_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Packing Locations Api Transaction Orders Config Packing Locations Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/transaction-orders/config/vendors": {"get": {"tags": ["Transaction Orders"], "summary": "Get Vendors List", "description": "Get distinct vendors for dropdown.", "operationId": "get_vendors_list_api_transaction_orders_config_vendors_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Vendors List Api Transaction Orders Config Vendors Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/transaction-orders/config/consumables": {"get": {"tags": ["Transaction Orders"], "summary": "Get Consumables List", "description": "Get distinct consumables for dropdown.", "operationId": "get_consumables_list_api_transaction_orders_config_consumables_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Consumables List Api Transaction Orders Config Consumables Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/transaction-orders/recent": {"get": {"tags": ["Transaction Orders"], "summary": "Get Recent Consumable Transactions", "description": "Get recent consumable transactions for dashboard display.", "operationId": "get_recent_consumable_transactions_api_transaction_orders_recent_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Recent Consumable Transactions Api Transaction Orders Recent Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/rolling-cage/": {"get": {"tags": ["Rolling Cage"], "summary": "Get Rolling Cages", "description": "Get all rolling cages with filtering and pagination.", "operationId": "get_rolling_cages_api_rolling_cage__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election"}}, {"name": "to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To"}}, {"name": "packed", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Packed"}}, {"name": "unpacked", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Unpacked"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "sortBy", "in": "query", "required": false, "schema": {"type": "string", "default": "created_at", "title": "<PERSON><PERSON><PERSON>"}}, {"name": "sortOrder", "in": "query", "required": false, "schema": {"type": "string", "default": "DESC", "title": "Sortorder"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Rolling Cages Api Rolling Cage  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Rolling Cage"], "summary": "Create Rolling Cage", "description": "Create new rolling cage.", "operationId": "create_rolling_cage_api_rolling_cage__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RollingCageCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Rolling Cage Api Rolling Cage  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/rolling-cage/{cage_id}": {"get": {"tags": ["Rolling Cage"], "summary": "Get Rolling Cage", "description": "Get rolling cage by ID.", "operationId": "get_rolling_cage_api_rolling_cage__cage_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cage_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cage Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Rolling Cage Api Rolling Cage  Cage Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Rolling Cage"], "summary": "Update Rolling Cage", "description": "Update rolling cage.", "operationId": "update_rolling_cage_api_rolling_cage__cage_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cage_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cage Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RollingCageUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Rolling Cage Api Rolling Cage  Cage Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Rolling Cage"], "summary": "Delete Rolling Cage", "description": "Delete rolling cage.", "operationId": "delete_rolling_cage_api_rolling_cage__cage_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cage_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cage Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/rolling-cage/bulk-update": {"post": {"tags": ["Rolling Cage"], "summary": "Bulk Update Rolling Cages", "description": "Bulk operations for rolling cages.", "operationId": "bulk_update_rolling_cages_api_rolling_cage_bulk_update_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RollingCageBulkUpdate"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Bulk Update Rolling Cages Api Rolling Cage Bulk Update Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/rolling-cage/bulk-delete": {"delete": {"tags": ["Rolling Cage"], "summary": "Bulk Delete Rolling Cages", "description": "Bulk delete rolling cages.", "operationId": "bulk_delete_rolling_cages_api_rolling_cage_bulk_delete_delete", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Delete Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/rolling-cage/stats/summary": {"get": {"tags": ["Rolling Cage"], "summary": "Get Rolling Cage Statistics", "description": "Get rolling cage statistics.", "operationId": "get_rolling_cage_statistics_api_rolling_cage_stats_summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election"}}, {"name": "to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Rolling Cage Statistics Api Rolling Cage Stats Summary Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/rolling-cage/filters/options": {"get": {"tags": ["Rolling Cage"], "summary": "Get Filter Options", "description": "Get unique values for filters.", "operationId": "get_filter_options_api_rolling_cage_filters_options_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Filter Options Api Rolling Cage Filters Options Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/rolling-cage/{cage_id}/toggle-packed": {"patch": {"tags": ["Rolling Cage"], "summary": "Toggle Packed Status", "description": "Toggle packed status.", "operationId": "toggle_packed_status_api_rolling_cage__cage_id__toggle_packed_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cage_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cage Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Toggle Packed Status Api Rolling Cage  Cage Id  Toggle Packed Patch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/rolling-cage/{cage_id}/toggle-unpacked": {"patch": {"tags": ["Rolling Cage"], "summary": "Toggle Unpacked Status", "description": "Toggle unpacked status.", "operationId": "toggle_unpacked_status_api_rolling_cage__cage_id__toggle_unpacked_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cage_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cage Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Toggle Unpacked Status Api Rolling Cage  Cage Id  Toggle Unpacked Patch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/locations/": {"get": {"tags": ["Masters Locations"], "summary": "Get Locations", "description": "Get all masters locations with filtering and pagination.\nEquivalent to GET / in masters location Node.js version.", "operationId": "get_locations_api_masters_locations__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}}, {"name": "source", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Source"}}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Locations Api Masters Locations  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Masters Locations"], "summary": "Create Location", "description": "Create new location.\nEquivalent to POST / in masters location Node.js version.", "operationId": "create_location_api_masters_locations__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Location Api Masters Locations  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/locations/{location_id}": {"get": {"tags": ["Masters Locations"], "summary": "Get Location", "description": "Get location by ID.\nEquivalent to GET /:id in masters location Node.js version.", "operationId": "get_location_api_masters_locations__location_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "location_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Location Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Location Api Masters Locations  Location Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Masters Locations"], "summary": "Update Location", "description": "Update location.\nEquivalent to PUT /:id in masters location Node.js version.", "operationId": "update_location_api_masters_locations__location_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "location_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Location Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Location Api Masters Locations  Location Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Masters Locations"], "summary": "Delete Location", "description": "Delete location.\nEquivalent to DELETE /:id in masters location Node.js version.", "operationId": "delete_location_api_masters_locations__location_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "location_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Location Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/locations/asset-types": {"get": {"tags": ["Masters Locations"], "summary": "Get Asset Types", "description": "Get unique asset types from locations.\nThis endpoint provides the asset types for filtering.", "operationId": "get_asset_types_api_masters_locations_asset_types_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Asset Types Api Masters Locations Asset Types Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/masters/locations/types/list": {"get": {"tags": ["Masters Locations"], "summary": "Get Location Types", "description": "Get unique location types.", "operationId": "get_location_types_api_masters_locations_types_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response Get Location Types Api Masters Locations Types List Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/masters/locations/sources/list": {"get": {"tags": ["Masters Locations"], "summary": "Get Location Sources", "description": "Get available location sources.", "operationId": "get_location_sources_api_masters_locations_sources_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response Get Location Sources Api Masters Locations Sources List Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/masters/locations/sync-from-configuration": {"post": {"tags": ["Masters Locations"], "summary": "Sync From Configuration", "description": "Sync locations from configuration module.\nCustom endpoint for syncing packing locations to masters.", "operationId": "sync_from_configuration_api_masters_locations_sync_from_configuration_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Sync Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/masters/locations/sync/{configuration_location_id}": {"delete": {"tags": ["Masters Locations"], "summary": "Remove Synced Location", "description": "Remove synced location by configuration location ID.", "operationId": "remove_synced_location_api_masters_locations_sync__configuration_location_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "configuration_location_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Configuration Location Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/locations/fix-enum-data": {"post": {"tags": ["Masters Locations"], "summary": "Fix Enum Data", "description": "Fix invalid enum values in the database.\nThis is a maintenance endpoint to clean up data.", "operationId": "fix_enum_data_api_masters_locations_fix_enum_data_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-types": {"get": {"tags": ["Asset Types"], "summary": "Get Asset Types", "description": "Get all asset types.\nEquivalent to GET / in Node.js assetTypes.ts", "operationId": "get_asset_types_api_asset_types_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Asset Types"], "summary": "Create Asset Type", "description": "Create new asset type.\nEquivalent to POST / in Node.js assetTypes.ts", "operationId": "create_asset_type_api_asset_types_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Asset Type Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-types/": {"get": {"tags": ["Asset Types"], "summary": "Get Asset Types", "description": "Get all asset types.\nEquivalent to GET / in Node.js assetTypes.ts", "operationId": "get_asset_types_api_asset_types__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Asset Types"], "summary": "Create Asset Type", "description": "Create new asset type.\nEquivalent to POST / in Node.js assetTypes.ts", "operationId": "create_asset_type_api_asset_types__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Asset Type Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-types/{type_id}": {"get": {"tags": ["Asset Types"], "summary": "Get Asset Type", "description": "Get asset type by ID.\nEquivalent to GET /:id in Node.js assetTypes.ts", "operationId": "get_asset_type_api_asset_types__type_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Asset Types"], "summary": "Update Asset Type", "description": "Update asset type.\nEquivalent to PUT /:id in Node.js assetTypes.ts", "operationId": "update_asset_type_api_asset_types__type_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Asset Type Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Asset Types"], "summary": "Delete Asset Type", "description": "Delete asset type.\nEquivalent to DELETE /:id in Node.js assetTypes.ts", "operationId": "delete_asset_type_api_asset_types__type_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-models": {"get": {"tags": ["Asset Models"], "summary": "Get Asset Models", "description": "Get all asset models.\nEquivalent to GET / in Node.js assetModel.ts", "operationId": "get_asset_models_api_asset_models_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Asset Models"], "summary": "Create Asset Model", "description": "Create new asset model.\nEquivalent to POST / in Node.js assetModel.ts", "operationId": "create_asset_model_api_asset_models_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Asset Model Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-models/": {"get": {"tags": ["Asset Models"], "summary": "Get Asset Models", "description": "Get all asset models.\nEquivalent to GET / in Node.js assetModel.ts", "operationId": "get_asset_models_api_asset_models__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Asset Models"], "summary": "Create Asset Model", "description": "Create new asset model.\nEquivalent to POST / in Node.js assetModel.ts", "operationId": "create_asset_model_api_asset_models__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Asset Model Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-models/{model_id}": {"get": {"tags": ["Asset Models"], "summary": "Get Asset Model", "description": "Get asset model by ID.\nEquivalent to GET /:id in Node.js assetModel.ts", "operationId": "get_asset_model_api_asset_models__model_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "model_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Model Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Asset Models"], "summary": "Update Asset Model", "description": "Update asset model.\nEquivalent to PUT /:id in Node.js assetModel.ts", "operationId": "update_asset_model_api_asset_models__model_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "model_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Model Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Asset Model Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Asset Models"], "summary": "Delete Asset Model", "description": "Delete asset model.\nEquivalent to DELETE /:id in Node.js assetModel.ts", "operationId": "delete_asset_model_api_asset_models__model_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "model_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Model Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-models/export": {"get": {"tags": ["Asset Models"], "summary": "Export Asset Models", "description": "Export asset models.\nEquivalent to GET /export in Node.js assetModel.ts", "operationId": "export_asset_models_api_asset_models_export_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-status": {"get": {"tags": ["Asset Status"], "summary": "Get Asset Statuses", "description": "Get all asset statuses.\nEquivalent to GET / in Node.js assetStatus.ts", "operationId": "get_asset_statuses_api_asset_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Asset Status"], "summary": "Create Asset Status", "description": "Create new asset status.\nEquivalent to POST / in Node.js assetStatus.ts", "operationId": "create_asset_status_api_asset_status_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Asset Status Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-status/": {"get": {"tags": ["Asset Status"], "summary": "Get Asset Statuses", "description": "Get all asset statuses.\nEquivalent to GET / in Node.js assetStatus.ts", "operationId": "get_asset_statuses_api_asset_status__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Asset Status"], "summary": "Create Asset Status", "description": "Create new asset status.\nEquivalent to POST / in Node.js assetStatus.ts", "operationId": "create_asset_status_api_asset_status__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Asset Status Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-status/{status_id}": {"get": {"tags": ["Asset Status"], "summary": "Get Asset Status", "description": "Get asset status by ID.\nEquivalent to GET /:id in Node.js assetStatus.ts", "operationId": "get_asset_status_api_asset_status__status_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Status Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Asset Status"], "summary": "Update Asset Status", "description": "Update asset status.\nEquivalent to PUT /:id in Node.js assetStatus.ts", "operationId": "update_asset_status_api_asset_status__status_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Status Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Asset Status Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Asset Status"], "summary": "Delete Asset Status", "description": "Delete asset status.\nEquivalent to DELETE /:id in Node.js assetStatus.ts", "operationId": "delete_asset_status_api_asset_status__status_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Status Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-status/export": {"get": {"tags": ["Asset Status"], "summary": "Export Asset Statuses", "description": "Export asset statuses.\nEquivalent to GET /export in Node.js assetStatus.ts", "operationId": "export_asset_statuses_api_asset_status_export_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/consumables-category": {"get": {"tags": ["Consumables Category"], "summary": "Get Consumables Categories", "description": "Get all consumables categories.\nEquivalent to GET / in Node.js consumablescategory.ts", "operationId": "get_consumables_categories_api_consumables_category_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Consumables Category"], "summary": "Create Consumables Category", "description": "Create new consumables category.\nEquivalent to POST / in Node.js consumablescategory.ts", "operationId": "create_consumables_category_api_consumables_category_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Category Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/consumables-category/": {"get": {"tags": ["Consumables Category"], "summary": "Get Consumables Categories", "description": "Get all consumables categories.\nEquivalent to GET / in Node.js consumablescategory.ts", "operationId": "get_consumables_categories_api_consumables_category__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Consumables Category"], "summary": "Create Consumables Category", "description": "Create new consumables category.\nEquivalent to POST / in Node.js consumablescategory.ts", "operationId": "create_consumables_category_api_consumables_category__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Category Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/consumables-category/{category_id}": {"get": {"tags": ["Consumables Category"], "summary": "Get Consumables Category", "description": "Get consumables category by ID.\nEquivalent to GET /:id in Node.js consumablescategory.ts", "operationId": "get_consumables_category_api_consumables_category__category_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Category Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Consumables Category"], "summary": "Update Consumables Category", "description": "Update consumables category.\nEquivalent to PUT /:id in Node.js consumablescategory.ts", "operationId": "update_consumables_category_api_consumables_category__category_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Category Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Category Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Consumables Category"], "summary": "Delete Consumables Category", "description": "Delete consumables category.\nEquivalent to DELETE /:id in Node.js consumablescategory.ts", "operationId": "delete_consumables_category_api_consumables_category__category_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Category Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/other-cost-types": {"get": {"tags": ["Other Cost Types"], "summary": "Get Other Cost Types", "description": "Get all other cost types.\nEquivalent to GET / in Node.js otherCostTypes.ts", "operationId": "get_other_cost_types_api_other_cost_types_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Other Cost Types"], "summary": "Create Other Cost Type", "description": "Create new other cost type.\nEquivalent to POST / in Node.js otherCostTypes.ts", "operationId": "create_other_cost_type_api_other_cost_types_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Cost Type Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/other-cost-types/": {"get": {"tags": ["Other Cost Types"], "summary": "Get Other Cost Types", "description": "Get all other cost types.\nEquivalent to GET / in Node.js otherCostTypes.ts", "operationId": "get_other_cost_types_api_other_cost_types__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Other Cost Types"], "summary": "Create Other Cost Type", "description": "Create new other cost type.\nEquivalent to POST / in Node.js otherCostTypes.ts", "operationId": "create_other_cost_type_api_other_cost_types__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Cost Type Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/other-cost-types/{cost_type_id}": {"get": {"tags": ["Other Cost Types"], "summary": "Get Other Cost Type", "description": "Get other cost type by ID.\nEquivalent to GET /:id in Node.js otherCostTypes.ts", "operationId": "get_other_cost_type_api_other_cost_types__cost_type_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cost_type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cost Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Other Cost Types"], "summary": "Update Other Cost Type", "description": "Update other cost type.\nEquivalent to PUT /:id in Node.js otherCostTypes.ts", "operationId": "update_other_cost_type_api_other_cost_types__cost_type_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cost_type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cost Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Cost Type Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Other Cost Types"], "summary": "Delete Other Cost Type", "description": "Delete other cost type.\nEquivalent to DELETE /:id in Node.js otherCostTypes.ts", "operationId": "delete_other_cost_type_api_other_cost_types__cost_type_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "cost_type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Cost Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/election-types": {"get": {"tags": ["Election Types"], "summary": "Get Election Types", "description": "Get all election types.\nEquivalent to GET / in Node.js electionTypes.ts", "operationId": "get_election_types_api_election_types_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Election Types"], "summary": "Create Election Type", "description": "Create new election type.\nEquivalent to POST / in Node.js electionTypes.ts", "operationId": "create_election_type_api_election_types_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Election Type Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/election-types/": {"get": {"tags": ["Election Types"], "summary": "Get Election Types", "description": "Get all election types.\nEquivalent to GET / in Node.js electionTypes.ts", "operationId": "get_election_types_api_election_types__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Election Types"], "summary": "Create Election Type", "description": "Create new election type.\nEquivalent to POST / in Node.js electionTypes.ts", "operationId": "create_election_type_api_election_types__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Election Type Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/election-types/{type_id}": {"get": {"tags": ["Election Types"], "summary": "Get Election Type", "description": "Get election type by ID.\nEquivalent to GET /:id in Node.js electionTypes.ts", "operationId": "get_election_type_api_election_types__type_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Election Types"], "summary": "Update Election Type", "description": "Update election type.\nEquivalent to PUT /:id in Node.js electionTypes.ts", "operationId": "update_election_type_api_election_types__type_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Election Type Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Election Types"], "summary": "Delete Election Type", "description": "Delete election type.\nEquivalent to DELETE /:id in Node.js electionTypes.ts", "operationId": "delete_election_type_api_election_types__type_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/elections": {"get": {"tags": ["Elections"], "summary": "Get Elections", "description": "Get all elections with pagination and search.", "operationId": "get_elections_api_elections_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string", "default": "", "title": "Search"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Elections"], "summary": "Create Election", "description": "Create a new election.", "operationId": "create_election_api_elections_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/elections/": {"get": {"tags": ["Elections"], "summary": "Get Elections", "description": "Get all elections with pagination and search.", "operationId": "get_elections_api_elections__get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 10, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"type": "string", "default": "", "title": "Search"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Elections"], "summary": "Create Election", "description": "Create a new election.", "operationId": "create_election_api_elections__post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/elections/{election_id}": {"get": {"tags": ["Elections"], "summary": "Get Election", "description": "Get election by ID with all related data.", "operationId": "get_election_api_elections__election_id__get", "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Elections"], "summary": "Update Election", "description": "Update an existing election.", "operationId": "update_election_api_elections__election_id__put", "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionDetailResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Elections"], "summary": "Delete Election", "description": "Delete an election.", "operationId": "delete_election_api_elections__election_id__delete", "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/elections/{election_id}/files": {"post": {"tags": ["Elections"], "summary": "Upload Election File", "description": "Upload a file for an election.", "operationId": "upload_election_file_api_elections__election_id__files_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}], "requestBody": {"required": true, "content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_election_file_api_elections__election_id__files_post"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Elections"], "summary": "Get Election Files", "description": "Get all files for an election.", "operationId": "get_election_files_api_elections__election_id__files_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/elections/{election_id}/files/{file_id}": {"delete": {"tags": ["Elections"], "summary": "Delete Election File", "description": "Delete an election file.", "operationId": "delete_election_file_api_elections__election_id__files__file_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}, {"name": "file_id", "in": "path", "required": true, "schema": {"type": "string", "title": "File Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/elections/{election_id}/notes": {"post": {"tags": ["Elections"], "summary": "Create Election Note", "description": "Create a note for an election.", "operationId": "create_election_note_api_elections__election_id__notes_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ElectionNoteCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Elections"], "summary": "Get Election Notes", "description": "Get all notes for an election.", "operationId": "get_election_notes_api_elections__election_id__notes_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Election Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-locations/": {"get": {"tags": ["Packing Locations"], "summary": "Get Packing Locations", "description": "Get all packing locations.\nEquivalent to GET / in Node.js packingLocation.ts", "operationId": "get_packing_locations_api_packing_locations__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Packing Locations"], "summary": "Create Packing Location", "description": "Create new packing location.\nEquivalent to POST / in Node.js packingLocation.ts", "operationId": "create_packing_location_api_packing_locations__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Location Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/packing-locations/{location_id}": {"get": {"tags": ["Packing Locations"], "summary": "Get Packing Location", "description": "Get packing location by ID.\nEquivalent to GET /:id in Node.js packingLocation.ts", "operationId": "get_packing_location_api_packing_locations__location_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "location_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Location Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Packing Locations"], "summary": "Update Packing Location", "description": "Update packing location.\nEquivalent to PUT /:id in Node.js packingLocation.ts", "operationId": "update_packing_location_api_packing_locations__location_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "location_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Location Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Location Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Packing Locations"], "summary": "Delete Packing Location", "description": "Delete packing location.\nEquivalent to DELETE /:id in Node.js packingLocation.ts", "operationId": "delete_packing_location_api_packing_locations__location_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "location_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Location Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/service-maintenance-types/": {"get": {"tags": ["Service Maintenance Types"], "summary": "Get Service Maintenance Types", "description": "Get all service maintenance types.\nEquivalent to GET / in Node.js serviceMaintenanceTypes.ts", "operationId": "get_service_maintenance_types_api_service_maintenance_types__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Service Maintenance Types"], "summary": "Create Service Maintenance Type", "description": "Create new service maintenance type.\nEquivalent to POST / in Node.js serviceMaintenanceTypes.ts", "operationId": "create_service_maintenance_type_api_service_maintenance_types__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Service Type Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/service-maintenance-types/{service_type_id}": {"get": {"tags": ["Service Maintenance Types"], "summary": "Get Service Maintenance Type", "description": "Get service maintenance type by ID.\nEquivalent to GET /:id in Node.js serviceMaintenanceTypes.ts", "operationId": "get_service_maintenance_type_api_service_maintenance_types__service_type_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "service_type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Service Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Service Maintenance Types"], "summary": "Update Service Maintenance Type", "description": "Update service maintenance type.\nEquivalent to PUT /:id in Node.js serviceMaintenanceTypes.ts", "operationId": "update_service_maintenance_type_api_service_maintenance_types__service_type_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "service_type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Service Type Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Service Type Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Service Maintenance Types"], "summary": "Delete Service Maintenance Type", "description": "Delete service maintenance type.\nEquivalent to DELETE /:id in Node.js serviceMaintenanceTypes.ts", "operationId": "delete_service_maintenance_type_api_service_maintenance_types__service_type_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "service_type_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Service Type Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/assets/": {"get": {"tags": ["Assets"], "summary": "Get Assets", "description": "Get all assets with optional filtering and pagination.\nEquivalent to GET /api/assets in Node.js version.", "operationId": "get_assets_api_assets__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AssetStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "condition", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/AssetCondition"}, {"type": "null"}], "title": "Condition"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "county", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "County"}}, {"name": "precinct", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Precinct"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Assets Api Assets  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Assets"], "summary": "Create Asset", "description": "Create new asset with RBAC validation.\nEquivalent to POST /api/assets in Node.js version.", "operationId": "create_asset_api_assets__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Asset Api Assets  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/assets/stats": {"get": {"tags": ["Assets"], "summary": "Get Asset Stats", "description": "Get asset statistics for dashboard.\nEquivalent to GET /api/assets/stats in Node.js version.", "operationId": "get_asset_stats_api_assets_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/assets/{asset_id}": {"get": {"tags": ["Assets"], "summary": "Get Asset", "description": "Get specific asset by ID with RBAC access control.\nEquivalent to GET /api/assets/:id in Node.js version.", "operationId": "get_asset_api_assets__asset_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Asset Api Assets  Asset Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Assets"], "summary": "Update Asset", "description": "Update asset.\nEquivalent to PUT /api/assets/:id in Node.js version.", "operationId": "update_asset_api_assets__asset_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Asset Api Assets  Asset Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Assets"], "summary": "Delete Asset", "description": "Delete asset.\nEquivalent to DELETE /api/assets/:id in Node.js version.", "operationId": "delete_asset_api_assets__asset_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/assets/by-asset-id/{asset_asset_id}": {"get": {"tags": ["Assets"], "summary": "Get Asset By Asset Id", "description": "Get asset by asset_id field.\nEquivalent to GET /api/assets/by-asset-id/:assetId in Node.js version.", "operationId": "get_asset_by_asset_id_api_assets_by_asset_id__asset_asset_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_asset_id", "in": "path", "required": true, "schema": {"type": "string", "title": "As<PERSON> Asset Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Asset By Asset Id Api Assets By Asset Id  Asset Asset Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/assets/{asset_id}/status": {"put": {"tags": ["Assets"], "summary": "Update Asset Status", "description": "Update asset status.\nEquivalent to PUT /api/assets/:id/status in Node.js version.", "operationId": "update_asset_status_api_assets__asset_id__status_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetStatusUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Asset Status Api Assets  Asset Id  Status Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/assets/locations/list": {"get": {"tags": ["Assets"], "summary": "Get Asset Locations", "description": "Get unique asset locations.\nEquivalent to GET /api/assets/locations/list in Node.js version.", "operationId": "get_asset_locations_api_assets_locations_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response Get Asset Locations Api Assets Locations List Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/assets/types/list": {"get": {"tags": ["Assets"], "summary": "Get Asset Types", "description": "Get unique asset types.\nEquivalent to GET /api/assets/types/list in Node.js version.", "operationId": "get_asset_types_api_assets_types_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response Get Asset Types Api Assets Types List Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/la-checklist/available-assets": {"get": {"tags": ["L&A Checklist"], "summary": "Get Available Assets For La Checklist", "description": "Get assets that are eligible for L&A checklist (New or Ready status).", "operationId": "get_available_assets_for_la_checklist_api_la_checklist_available_assets_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "object"}, "type": "array", "title": "Response Get Available Assets For La Checklist Api La Checklist Available Assets Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/la-checklist/sessions": {"post": {"tags": ["L&A Checklist"], "summary": "Start La Checklist Session", "description": "Start a new L&A checklist session for an asset.", "operationId": "start_la_checklist_session_api_la_checklist_sessions_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LAChecklistSessionStart"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LAChecklistSessionWithItems"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["L&A Checklist"], "summary": "Get La Checklist Sessions", "description": "Get L&A checklist sessions with optional filtering.", "operationId": "get_la_checklist_sessions_api_la_checklist_sessions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Asset Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/LAChecklistSessionStatus"}, {"type": "null"}], "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LAChecklistSessionResponse"}, "title": "Response Get La Checklist Sessions Api La Checklist Sessions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/la-checklist/sessions/{session_id}": {"get": {"tags": ["L&A Checklist"], "summary": "Get La Checklist Session", "description": "Get a specific L&A checklist session with items.", "operationId": "get_la_checklist_session_api_la_checklist_sessions__session_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LAChecklistSessionWithItems"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/la-checklist/items/{item_id}": {"put": {"tags": ["L&A Checklist"], "summary": "Update Checklist Item", "description": "Update a checklist item status.", "operationId": "update_checklist_item_api_la_checklist_items__item_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "item_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Item Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LAChecklistItemUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LAChecklistItemResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/la-checklist/sessions/{session_id}/complete": {"post": {"tags": ["L&A Checklist"], "summary": "Complete La Checklist Session", "description": "Complete an L&A checklist session and update asset status accordingly.\nThis endpoint evaluates all checklist items and updates the asset status to READY or FAILED.", "operationId": "complete_la_checklist_session_api_la_checklist_sessions__session_id__complete_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}, {"name": "notes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LAChecklistSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/la-checklist/sessions/{session_id}/summary": {"get": {"tags": ["L&A Checklist"], "summary": "Get La Checklist Session Summary", "description": "Get summary statistics for an L&A checklist session.", "operationId": "get_la_checklist_session_summary_api_la_checklist_sessions__session_id__summary_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "session_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Session Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get La Checklist Session Summary Api La Checklist Sessions  Session Id  Summary Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-checklist/available-assets-for-packing": {"get": {"tags": ["Supply Checklist"], "summary": "Get Available Assets For Packing", "description": "Get assets that are available for packing (Ready status only).", "operationId": "get_available_assets_for_packing_api_supply_checklist_available_assets_for_packing_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "object"}, "type": "array", "title": "Response Get Available Assets For Packing Api Supply Checklist Available Assets For Packing Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/supply-checklist/packed-assets": {"get": {"tags": ["Supply Checklist"], "summary": "Get Packed Assets", "description": "Get assets that are packed and available for unpacking.", "operationId": "get_packed_assets_api_supply_checklist_packed_assets_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "container_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Container Type"}}, {"name": "container_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Container Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Packed Assets Api Supply Checklist Packed Assets Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-checklist/pack-assets": {"post": {"tags": ["Supply Checklist"], "summary": "Pack Assets With Workflow", "description": "Pack assets into packing list or rolling cage and update their status to PACKED.\nThis endpoint integrates with the workflow system to ensure proper status transitions.", "operationId": "pack_assets_with_workflow_api_supply_checklist_pack_assets_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackAssetsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Pack Assets With Workflow Api Supply Checklist Pack Assets Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/supply-checklist/unpack-assets": {"post": {"tags": ["Supply Checklist"], "summary": "Unpack Assets With Workflow", "description": "Unpack assets and update their status back to READY.\nThis endpoint integrates with the workflow system for proper status transitions.", "operationId": "unpack_assets_with_workflow_api_supply_checklist_unpack_assets_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnpackAssetsRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Unpack Assets With Workflow Api Supply Checklist Unpack Assets Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/supply-checklist/checklists": {"get": {"tags": ["Supply Checklist"], "summary": "Get Supply Checklists", "description": "Get supply checklists with optional filtering.", "operationId": "get_supply_checklists_api_supply_checklist_checklists_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checklist_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/SupplyChecklistType"}, {"type": "null"}], "title": "Checklist Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/SupplyChecklistStatus"}, {"type": "null"}], "title": "Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SupplyChecklistResponse"}, "title": "Response Get Supply Checklists Api Supply Checklist Checklists Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-checklist/checklists/{checklist_id}": {"get": {"tags": ["Supply Checklist"], "summary": "Get Supply Checklist", "description": "Get a specific supply checklist with items.", "operationId": "get_supply_checklist_api_supply_checklist_checklists__checklist_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checklist_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checklist Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SupplyChecklistWithItems"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-checklist/dropdown-data": {"get": {"tags": ["Supply Checklist"], "summary": "Get Dropdown Data", "description": "Get dropdown data for elections and locations", "operationId": "get_dropdown_data_api_supply_checklist_dropdown_data_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Dropdown Data Api Supply Checklist Dropdown Data Get"}}}}}}}, "/api/supply-checklist/election-readiness-data": {"get": {"tags": ["Supply Checklist"], "summary": "Get Election Readiness Data", "description": "Get election readiness data from real packing list and rolling cage workflows", "operationId": "get_election_readiness_data_api_supply_checklist_election_readiness_data_get", "parameters": [{"name": "election_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election Id"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Election Readiness Data Api Supply Checklist Election Readiness Data Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-checklist/workflow-status": {"get": {"tags": ["Supply Checklist"], "summary": "Get Workflow Status", "description": "Check the status of workflow data in the database", "operationId": "get_workflow_status_api_supply_checklist_workflow_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Workflow Status Api Supply Checklist Workflow Status Get"}}}}}}}, "/api/supply-checklist/create-sample-data": {"post": {"tags": ["Supply Checklist"], "summary": "Create Sample Workflow Data", "description": "Create sample workflow data for testing", "operationId": "create_sample_workflow_data_api_supply_checklist_create_sample_data_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Sample Workflow Data Api Supply Checklist Create Sample Data Post"}}}}}}}, "/api/asset-status-history/asset/{asset_id}": {"get": {"tags": ["Asset Status History"], "summary": "Get Asset Status History", "description": "Get status history for a specific asset.", "operationId": "get_asset_status_history_api_asset_status_history_asset__asset_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/app__schemas__asset_status_history__AssetStatusHistoryResponse"}, "title": "Response Get Asset Status History Api Asset Status History Asset  Asset Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-status-history/": {"get": {"tags": ["Asset Status History"], "summary": "Get All Status History", "description": "Get status history with optional filtering.", "operationId": "get_all_status_history_api_asset_status_history__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 500, "minimum": 1, "default": 100, "title": "Limit"}}, {"name": "workflow_module", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Module"}}, {"name": "new_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "New Status"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/app__schemas__asset_status_history__AssetStatusHistoryResponse"}, "title": "Response Get All Status History Api Asset Status History  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-status-history/change-status": {"post": {"tags": ["Asset Status History"], "summary": "Change Asset Status", "description": "Manually change an asset's status and log the change.", "operationId": "change_asset_status_api_asset_status_history_change_status_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetStatusChangeRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/app__schemas__asset_status_history__AssetStatusHistoryResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-status-history/workflow-modules": {"get": {"tags": ["Asset Status History"], "summary": "Get Workflow Modules", "description": "Get list of distinct workflow modules from status history.", "operationId": "get_workflow_modules_api_asset_status_history_workflow_modules_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"type": "string"}, "type": "array", "title": "Response Get Workflow Modules Api Asset Status History Workflow Modules Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-status-history/status-summary": {"get": {"tags": ["Asset Status History"], "summary": "Get Status Summary", "description": "Get summary of asset statuses across the system.", "operationId": "get_status_summary_api_asset_status_history_status_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Status Summary Api Asset Status History Status Summary Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/asset-status-history/recent": {"get": {"tags": ["Asset Status History"], "summary": "Get Recent Asset Transactions", "description": "Get recent asset status changes for transaction display.", "operationId": "get_recent_asset_transactions_api_asset_status_history_recent_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "minimum": 1, "default": 10, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}, "title": "Response Get Recent Asset Transactions Api Asset Status History Recent Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflow/transition": {"post": {"tags": ["Workflow"], "summary": "Transition Asset Status", "description": "Transition an asset's status with workflow validation.\nThis is the central endpoint for all status changes.", "operationId": "transition_asset_status_api_workflow_transition_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetStatusTransitionRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetStatusTransitionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/workflow/validate": {"post": {"tags": ["Workflow"], "summary": "Validate Transition", "description": "Validate if an asset status transition is allowed without performing it.\nUseful for UI validation before submitting the actual transition.", "operationId": "validate_transition_api_workflow_validate_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowValidationRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowValidationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/workflow/asset/{asset_id}/valid-transitions": {"get": {"tags": ["Workflow"], "summary": "Get Valid Transitions", "description": "Get all valid transitions for an asset given its current status and workflow context.", "operationId": "get_valid_transitions_api_workflow_asset__asset_id__valid_transitions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}, {"name": "workflow_module", "in": "query", "required": true, "schema": {"allOf": [{"$ref": "#/components/schemas/WorkflowModule"}], "description": "Workflow module context", "title": "Workflow Module"}, "description": "Workflow module context"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ValidTransitionsResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflow/asset/{asset_id}/history": {"get": {"tags": ["Workflow"], "summary": "Get Asset Status History", "description": "Get status change history for an asset.", "operationId": "get_asset_status_history_api_workflow_asset__asset_id__history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "description": "Number of history records to return", "default": 50, "title": "Limit"}, "description": "Number of history records to return"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/app__schemas__workflow__AssetStatusHistoryResponse"}, "title": "Response Get Asset Status History Api Workflow Asset  Asset Id  History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflow/transitions": {"get": {"tags": ["Workflow"], "summary": "Get Workflow Transitions", "description": "Get workflow state transitions with optional filtering.", "operationId": "get_workflow_transitions_api_workflow_transitions_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "workflow_module", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/WorkflowModule"}, {"type": "null"}], "description": "Filter by workflow module", "title": "Workflow Module"}, "description": "Filter by workflow module"}, {"name": "from_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by from status", "title": "From Status"}, "description": "Filter by from status"}, {"name": "to_status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by to status", "title": "To Status"}, "description": "Filter by to status"}, {"name": "is_active", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Filter by active status", "default": true, "title": "Is Active"}, "description": "Filter by active status"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/WorkflowStateTransitionResponse"}, "title": "Response Get Workflow Transitions Api Workflow Transitions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflow/assets/by-status": {"get": {"tags": ["Workflow"], "summary": "Get Assets By Status For Workflow", "description": "Get assets with specific status that can be processed by a workflow module.\nUseful for populating dropdowns in workflow UIs.", "operationId": "get_assets_by_status_for_workflow_api_workflow_assets_by_status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": true, "schema": {"allOf": [{"$ref": "#/components/schemas/AssetStatus"}], "description": "Asset status to filter by", "title": "Status"}, "description": "Asset status to filter by"}, {"name": "workflow_module", "in": "query", "required": true, "schema": {"allOf": [{"$ref": "#/components/schemas/WorkflowModule"}], "description": "Workflow module context", "title": "Workflow Module"}, "description": "Workflow module context"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Assets By Status For Workflow Api Workflow Assets By Status Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/workflow/stats/status-summary": {"get": {"tags": ["Workflow"], "summary": "Get Status Summary", "description": "Get summary of assets by status.", "operationId": "get_status_summary_api_workflow_stats_status_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Status Summary Api Workflow Stats Status Summary Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/workflow/transitions/{transition_id}/toggle": {"post": {"tags": ["Workflow"], "summary": "Toggle Transition Status", "description": "Toggle the active status of a workflow transition (admin only).", "operationId": "toggle_transition_status_api_workflow_transitions__transition_id__toggle_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "transition_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Transition Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowStateTransitionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-transfers/": {"post": {"tags": ["Asset Transfers"], "summary": "Create Asset Transfer", "description": "Create a new asset transfer with RBAC validation.\nInitiates the transfer process and updates asset status to IN_TRANSFER.", "operationId": "create_asset_transfer_api_asset_transfers__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetTransferCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetTransferResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Asset Transfers"], "summary": "Get Asset Transfers", "description": "Get asset transfers with RBAC filtering and pagination.", "operationId": "get_asset_transfers_api_asset_transfers__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "asset_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Asset Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TransferStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "transfer_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/TransferType"}, {"type": "null"}], "title": "Transfer Type"}}, {"name": "from_location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "From Location"}}, {"name": "to_location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To Location"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Asset Transfers Api Asset Transfers  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-transfers/{transfer_id}": {"get": {"tags": ["Asset Transfers"], "summary": "Get Asset Transfer", "description": "Get a specific asset transfer by ID.", "operationId": "get_asset_transfer_api_asset_transfers__transfer_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "transfer_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Transfer Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetTransferResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Asset Transfers"], "summary": "Update Asset Transfer", "description": "Update asset transfer details and status.\nHandles status transitions with proper workflow validation.", "operationId": "update_asset_transfer_api_asset_transfers__transfer_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "transfer_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Transfer Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetTransferUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetTransferResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-transfers/{transfer_id}/verify": {"post": {"tags": ["Asset Transfers"], "summary": "Verify Asset Transfer", "description": "Verify asset transfer delivery.\nUpdates transfer status to DELIVERED and asset status accordingly.", "operationId": "verify_asset_transfer_api_asset_transfers__transfer_id__verify_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "transfer_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Transfer Id"}}, {"name": "verification_code", "in": "query", "required": true, "schema": {"type": "string", "description": "Verification code", "title": "Verification Code"}, "description": "Verification code"}, {"name": "condition_after_transfer", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Asset condition after transfer", "title": "Condition After Transfer"}, "description": "Asset condition after transfer"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssetTransferResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/asset-transfers/asset/{asset_id}/history": {"get": {"tags": ["Asset Transfers"], "summary": "Get Asset Transfer History", "description": "Get transfer history for a specific asset.", "operationId": "get_asset_transfer_history_api_asset_transfers_asset__asset_id__history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AssetTransferResponse"}, "title": "Response Get Asset Transfer History Api Asset Transfers Asset  Asset Id  History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/damage-reports/": {"post": {"tags": ["Damage Reports"], "summary": "Create Damage Report", "description": "Create a new damage report.\nUpdates asset status to DAMAGED when significant damage is reported.", "operationId": "create_damage_report_api_damage_reports__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Damage Reports"], "summary": "Get Damage Reports", "description": "Get damage reports with filtering and pagination.", "operationId": "get_damage_reports_api_damage_reports__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "asset_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Asset Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DamageReportStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "damage_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DamageType"}, {"type": "null"}], "title": "Damage Type"}}, {"name": "damage_severity", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/DamageSeverity"}, {"type": "null"}], "title": "Damage Severity"}}, {"name": "priority", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Priority"}}, {"name": "assigned_to", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assigned To"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Damage Reports Api Damage Reports  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/damage-reports/{report_id}": {"get": {"tags": ["Damage Reports"], "summary": "Get Damage Report", "description": "Get a specific damage report by ID.", "operationId": "get_damage_report_api_damage_reports__report_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "report_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Report Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Damage Reports"], "summary": "Update Damage Report", "description": "Update damage report details and status.\nHandles repair completion and asset status transitions.", "operationId": "update_damage_report_api_damage_reports__report_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "report_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Report Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/damage-reports/{report_id}/assign": {"post": {"tags": ["Damage Reports"], "summary": "Assign Damage Report", "description": "Assign damage report to a user (admin only).", "operationId": "assign_damage_report_api_damage_reports__report_id__assign_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "report_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Report Id"}}, {"name": "assigned_to", "in": "query", "required": true, "schema": {"type": "integer", "description": "User ID to assign the report to", "title": "Assigned To"}, "description": "User ID to assign the report to"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/damage-reports/asset/{asset_id}/history": {"get": {"tags": ["Damage Reports"], "summary": "Get Asset Damage History", "description": "Get damage report history for a specific asset.", "operationId": "get_asset_damage_history_api_damage_reports_asset__asset_id__history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DamageReportResponse"}, "title": "Response Get Asset Damage History Api Damage Reports Asset  Asset Id  History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/damage-reports/stats/summary": {"get": {"tags": ["Damage Reports"], "summary": "Get Damage Reports Summary", "description": "Get damage reports summary statistics.", "operationId": "get_damage_reports_summary_api_damage_reports_stats_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Damage Reports Summary Api Damage Reports Stats Summary Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/checkout-sessions/": {"post": {"tags": ["Checkout Sessions"], "summary": "Create Checkout Session", "description": "Create a new checkout session.\nUpdates asset status to CHECKED_OUT.", "operationId": "create_checkout_session_api_checkout_sessions__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Checkout Sessions"], "summary": "Get Checkout Sessions", "description": "Get checkout sessions with filtering and pagination.", "operationId": "get_checkout_sessions_api_checkout_sessions__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "asset_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Asset Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CheckoutStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "checkout_type", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/CheckoutType"}, {"type": "null"}], "title": "Checkout Type"}}, {"name": "personnel_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Personnel Name"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "overdue_only", "in": "query", "required": false, "schema": {"type": "boolean", "description": "Show only overdue checkouts", "default": false, "title": "Overdue Only"}, "description": "Show only overdue checkouts"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Checkout Sessions Api Checkout Sessions  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/checkout-sessions/{checkout_id}": {"get": {"tags": ["Checkout Sessions"], "summary": "Get Checkout Session", "description": "Get a specific checkout session by ID.", "operationId": "get_checkout_session_api_checkout_sessions__checkout_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Checkout Sessions"], "summary": "Update Checkout Session", "description": "Update checkout session.\n<PERSON>les return processing and status changes.", "operationId": "update_checkout_session_api_checkout_sessions__checkout_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/checkout-sessions/{checkout_id}/extend": {"post": {"tags": ["Checkout Sessions"], "summary": "Extend Checkout Session", "description": "Extend checkout session return date.", "operationId": "extend_checkout_session_api_checkout_sessions__checkout_id__extend_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}, {"name": "new_return_date", "in": "query", "required": true, "schema": {"type": "string", "format": "date-time", "description": "New expected return date", "title": "New Return Date"}, "description": "New expected return date"}, {"name": "extension_reason", "in": "query", "required": true, "schema": {"type": "string", "description": "Reason for extension", "title": "Extension Reason"}, "description": "Reason for extension"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/checkout-sessions/{checkout_id}/return": {"post": {"tags": ["Checkout Sessions"], "summary": "Return Asset", "description": "Process asset return.", "operationId": "return_asset_api_checkout_sessions__checkout_id__return_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}, {"name": "condition_at_return", "in": "query", "required": true, "schema": {"type": "string", "description": "Asset condition at return", "title": "Condition At Return"}, "description": "Asset condition at return"}, {"name": "return_notes", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Return notes", "title": "Return Notes"}, "description": "Return notes"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutSessionResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/checkout-sessions/overdue/list": {"get": {"tags": ["Checkout Sessions"], "summary": "Get Overdue Checkouts", "description": "Get all overdue checkout sessions.", "operationId": "get_overdue_checkouts_api_checkout_sessions_overdue_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CheckoutSessionResponse"}, "type": "array", "title": "Response Get Overdue Checkouts Api Checkout Sessions Overdue List Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/checkout-sessions/asset/{asset_id}/history": {"get": {"tags": ["Checkout Sessions"], "summary": "Get Asset Checkout History", "description": "Get checkout history for a specific asset.", "operationId": "get_asset_checkout_history_api_checkout_sessions_asset__asset_id__history_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Asset Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CheckoutSessionResponse"}, "title": "Response Get Asset Checkout History Api Checkout Sessions Asset  Asset Id  History Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/consumables/": {"get": {"tags": ["Masters Consumables"], "summary": "Get Consumables", "description": "Get all masters consumables with filtering and pagination.", "operationId": "get_consumables_api_masters_consumables__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "category_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}}, {"name": "is_critical", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Critical"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Consumables Api Masters Consumables  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Masters Consumables"], "summary": "Create Consumable", "description": "Create new consumable.", "operationId": "create_consumable_api_masters_consumables__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsumableCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Consumable Api Masters Consumables  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/consumables/{consumable_id}": {"get": {"tags": ["Masters Consumables"], "summary": "Get Consumable", "description": "Get single consumable by ID with stock details.", "operationId": "get_consumable_api_masters_consumables__consumable_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "consumable_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Consumable Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Consumable Api Masters Consumables  Consumable Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Masters Consumables"], "summary": "Update Consumable", "description": "Update consumable.", "operationId": "update_consumable_api_masters_consumables__consumable_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "consumable_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Consumable Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConsumableUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Consumable Api Masters Consumables  Consumable Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Masters Consumables"], "summary": "Delete Consumable", "description": "Delete consumable.", "operationId": "delete_consumable_api_masters_consumables__consumable_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "consumable_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Consumable Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/consumables/{consumable_id}/stock": {"get": {"tags": ["Masters Consumables"], "summary": "Get Consumable Stock", "description": "Get stock levels for a consumable.", "operationId": "get_consumable_stock_api_masters_consumables__consumable_id__stock_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "consumable_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Consumable Id"}}, {"name": "location_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location Id"}}, {"name": "location_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location Type"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Consumable Stock Api Masters Consumables  Consumable Id  Stock Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/consumables-checkout/": {"get": {"tags": ["Consumables Checkout"], "summary": "Get Checkouts", "description": "Get all consumable checkouts with filtering and pagination.", "operationId": "get_checkouts_api_consumables_checkout__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "default": 10, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "workflow_stage", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Stage"}}, {"name": "dateFrom", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Datefrom"}}, {"name": "dateTo", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Dateto"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Checkouts Api Consumables Checkout  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Consumables Checkout"], "summary": "Create Checkout", "description": "Create new consumable checkout.", "operationId": "create_checkout_api_consumables_checkout__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Checkout Api Consumables Checkout  Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/consumables-checkout/{checkout_id}": {"get": {"tags": ["Consumables Checkout"], "summary": "Get Checkout", "description": "Get single checkout by ID.", "operationId": "get_checkout_api_consumables_checkout__checkout_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Checkout Api Consumables Checkout  Checkout Id  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Consumables Checkout"], "summary": "Update Checkout", "description": "Update checkout.", "operationId": "update_checkout_api_consumables_checkout__checkout_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Checkout Api Consumables Checkout  Checkout Id  Put"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Consumables Checkout"], "summary": "Delete Checkout", "description": "Delete checkout.", "operationId": "delete_checkout_api_consumables_checkout__checkout_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/consumables-checkout/{checkout_id}/status": {"patch": {"tags": ["Consumables Checkout"], "summary": "Update Checkout Status", "description": "Update checkout status.", "operationId": "update_checkout_status_api_consumables_checkout__checkout_id__status_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "checkout_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Checkout Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckoutStatusUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Update Checkout Status Api Consumables Checkout  Checkout Id  Status Patch"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/consumables-checkout/workflow": {"post": {"tags": ["Consumables Checkout"], "summary": "Create Workflow Checkout", "description": "Create checkout from workflow (rolling cage or packing list).", "operationId": "create_workflow_checkout_api_consumables_checkout_workflow_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkflowCheckoutRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Workflow Checkout Api Consumables Checkout Workflow Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/consumables-checkout/summary/dashboard": {"get": {"tags": ["Consumables Checkout"], "summary": "Get Checkout Summary", "description": "Get checkout dashboard summary.", "operationId": "get_checkout_summary_api_consumables_checkout_summary_dashboard_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Checkout Summary Api Consumables Checkout Summary Dashboard Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/users/users": {"get": {"tags": ["User Management"], "summary": "Get Users", "description": "Get all users with optional filtering.\nRequires admin access or returns users based on current user's access level.", "operationId": "get_users_api_users_users_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "user_group", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Group"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}}, {"name": "access_level", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Access Level"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["User Management"], "summary": "Create User", "description": "Create a new user. Requires admin access.", "operationId": "create_user_api_users_users_post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/users/users/{user_id}": {"get": {"tags": ["User Management"], "summary": "Get User", "description": "Get a specific user by ID.", "operationId": "get_user_api_users_users__user_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["User Management"], "summary": "Update User", "description": "Update a user. Users can update themselves, admins can update anyone.", "operationId": "update_user_api_users_users__user_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["User Management"], "summary": "Delete User", "description": "Delete a user. Requires admin access.", "operationId": "delete_user_api_users_users__user_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "user_id", "in": "path", "required": true, "schema": {"type": "string", "title": "User Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/users/user-groups": {"get": {"tags": ["User Management"], "summary": "Get User Groups", "description": "Get all user groups with user counts.", "operationId": "get_user_groups_api_users_user_groups_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserGroupResponse"}, "type": "array", "title": "Response Get User Groups Api Users User Groups Get"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/users/users/stats/summary": {"get": {"tags": ["User Management"], "summary": "Get User Stats", "description": "Get user statistics summary.", "operationId": "get_user_stats_api_users_users_stats_summary_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/reports/analytics/dashboard": {"get": {"tags": ["Reports & Analytics"], "summary": "Get Analytics Dashboard", "description": "Get analytics dashboard data for reports.\nFrontend expects this for reports & analytics page.", "operationId": "get_analytics_dashboard_api_reports_analytics_dashboard_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/reports/custom/generate": {"get": {"tags": ["Reports & Analytics"], "summary": "Generate Custom Report", "description": "Generate custom reports based on parameters.\nFrontend expects this for custom reports functionality.", "operationId": "generate_custom_report_api_reports_custom_generate_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "report_type", "in": "query", "required": true, "schema": {"type": "string", "description": "Type of report to generate", "title": "Report Type"}, "description": "Type of report to generate"}, {"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Start date for report", "title": "Start Date"}, "description": "Start date for report"}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "End date for report", "title": "End Date"}, "description": "End date for report"}, {"name": "filters", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Additional filters as JSON", "title": "Filters"}, "description": "Additional filters as JSON"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/reports/audit/user-activity": {"get": {"tags": ["Reports & Analytics"], "summary": "Get User Activity Audit", "description": "Get user activity audit logs from database.\nFrontend expects this for audit reports.", "operationId": "get_user_activity_audit_api_reports_audit_user_activity_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "End Date"}}, {"name": "user_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "User Id"}}, {"name": "activity_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Activity Type"}}, {"name": "module", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/reports/audit/asset-movement": {"get": {"tags": ["Reports & Analytics"], "summary": "Get Asset Movement Audit", "description": "Get asset movement audit logs from asset transfers.\nFrontend expects this for audit reports.", "operationId": "get_asset_movement_audit_api_reports_audit_asset_movement_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "End Date"}}, {"name": "asset_id", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Asset Id"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/reports/audit/system-access": {"get": {"tags": ["Reports & Analytics"], "summary": "Get System Access Audit", "description": "Get system access audit logs.\nFrontend expects this for audit reports.", "operationId": "get_system_access_audit_api_reports_audit_system_access_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "End Date"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/reports/scheduled": {"get": {"tags": ["Reports & Analytics"], "summary": "Get Scheduled Reports", "description": "Get scheduled reports from database.", "operationId": "get_scheduled_reports_api_reports_scheduled_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/reports/export": {"post": {"tags": ["Reports & Analytics"], "summary": "Export Report", "description": "Export report data in various formats.\nFrontend expects this for report export functionality.", "operationId": "export_report_api_reports_export_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Export Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/reports/audit/stats": {"get": {"tags": ["Reports & Analytics"], "summary": "Get Audit Stats", "description": "Get audit statistics for the dashboard.", "operationId": "get_audit_stats_api_reports_audit_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/reports/audit/recent-activity": {"get": {"tags": ["Reports & Analytics"], "summary": "Get Recent Audit Activity", "description": "Get recent audit activity across all audit types.", "operationId": "get_recent_audit_activity_api_reports_audit_recent_activity_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 50, "default": 5, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/reports/audit/asset-scan": {"get": {"tags": ["Reports & Analytics"], "summary": "<PERSON><PERSON>", "description": "Get asset scan audit logs from database.", "operationId": "get_asset_scan_audit_api_reports_audit_asset_scan_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "start_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Start Date"}}, {"name": "end_date", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "End Date"}}, {"name": "scan_type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Scan Type"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "asset_tag", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "default": 100, "title": "Limit"}}, {"name": "offset", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "Offset"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/dev/list": {"get": {"tags": ["Maintenance"], "summary": "Get Maintenance Records Dev", "description": "Development endpoint - Get maintenance records without authentication.", "operationId": "get_maintenance_records_dev_api_maintenance_dev_list_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/dev/stats": {"get": {"tags": ["Maintenance"], "summary": "Get Maintenance Stats Dev", "description": "Development endpoint - Get maintenance stats without authentication.", "operationId": "get_maintenance_stats_dev_api_maintenance_dev_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/maintenance/dev/available-assets": {"get": {"tags": ["Maintenance"], "summary": "Get Available Assets Dev", "description": "Development endpoint - Get available assets without authentication.", "operationId": "get_available_assets_dev_api_maintenance_dev_available_assets_get", "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/": {"get": {"tags": ["Maintenance"], "summary": "Get Maintenance Records", "description": "Get all maintenance records with optional filtering.\nEquivalent to GET / in Node.js maintenance.ts", "operationId": "get_maintenance_records_api_maintenance__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "category", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MaintenanceCategory"}, {"type": "null"}], "title": "Category"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/MaintenanceStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "priority", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/Priority"}, {"type": "null"}], "title": "Priority"}}, {"name": "requestType", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/RequestType"}, {"type": "null"}], "title": "Requesttype"}}, {"name": "assignedTechnician", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assignedtechnician"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Startdate"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Enddate"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Maintenance"], "summary": "Create Maintenance Record", "description": "Create new maintenance request with workflow management.\nEquivalent to POST / in Node.js maintenance.ts\nNow includes automatic asset status transition to 'Under Maintenance'.", "operationId": "create_maintenance_record_api_maintenance__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Maintenance Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/stats": {"get": {"tags": ["Maintenance"], "summary": "Get Maintenance Stats", "description": "Get maintenance dashboard statistics.\nEquivalent to GET /stats in Node.js maintenance.ts", "operationId": "get_maintenance_stats_api_maintenance_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/maintenance/recent-activity": {"get": {"tags": ["Maintenance"], "summary": "Get Recent Maintenance Activity", "description": "Get recent maintenance activity.\nEquivalent to GET /recent-activity in Node.js maintenance.ts", "operationId": "get_recent_maintenance_activity_api_maintenance_recent_activity_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/maintenance/{asset_tag}": {"get": {"tags": ["Maintenance"], "summary": "Get Maintenance Record", "description": "Get specific maintenance record by asset tag.\nEquivalent to GET /:assetTag in Node.js maintenance.ts", "operationId": "get_maintenance_record_api_maintenance__asset_tag__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_tag", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Maintenance"], "summary": "Update Maintenance Record", "description": "Update maintenance record.\nEquivalent to PUT /:assetTag in Node.js maintenance.ts", "operationId": "update_maintenance_record_api_maintenance__asset_tag__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_tag", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Maintenance Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Maintenance"], "summary": "Delete Maintenance Record", "description": "Delete maintenance record.\nEquivalent to DELETE /:assetTag in Node.js maintenance.ts", "operationId": "delete_maintenance_record_api_maintenance__asset_tag__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_tag", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/{asset_tag}/status": {"put": {"tags": ["Maintenance"], "summary": "Update Maintenance Status", "description": "Update maintenance status with automatic asset status management.\nEquivalent to PUT /:assetTag/status in Node.js maintenance.ts\nNow includes automatic asset status transition when maintenance is completed.", "operationId": "update_maintenance_status_api_maintenance__asset_tag__status_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_tag", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Status Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/{asset_tag}/schedule": {"put": {"tags": ["Maintenance"], "summary": "Schedule Maintenance", "description": "Schedule maintenance.\nEquivalent to PUT /:assetTag/schedule in Node.js maintenance.ts", "operationId": "schedule_maintenance_api_maintenance__asset_tag__schedule_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_tag", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Schedule Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/overdue/list": {"get": {"tags": ["Maintenance"], "summary": "Get Overdue Maintenance", "description": "Get overdue maintenance tasks.\nEquivalent to GET /overdue/list in Node.js maintenance.ts", "operationId": "get_overdue_maintenance_api_maintenance_overdue_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/maintenance/categories/list": {"get": {"tags": ["Maintenance"], "summary": "Get Maintenance Categories", "description": "Get unique categories.\nEquivalent to GET /categories/list in Node.js maintenance.ts", "operationId": "get_maintenance_categories_api_maintenance_categories_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/maintenance/technicians/list": {"get": {"tags": ["Maintenance"], "summary": "Get Maintenance Technicians", "description": "Get unique technicians.\nEquivalent to GET /technicians/list in Node.js maintenance.ts", "operationId": "get_maintenance_technicians_api_maintenance_technicians_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/maintenance/available-assets": {"get": {"tags": ["Maintenance"], "summary": "Get Available Assets For Maintenance", "description": "Get assets available for maintenance requests.\nAssets with status 'Ready' or 'Failed' are available for maintenance.", "operationId": "get_available_assets_for_maintenance_api_maintenance_available_assets_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "type", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Type"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "county", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "County"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/maintenance/request": {"post": {"tags": ["Maintenance"], "summary": "Create Maintenance Request", "description": "Create a new maintenance request and automatically update asset status to 'Under Maintenance'.\nThis follows the workflow: User selects asset → Creates maintenance request → Asset status changes to maintenance.", "operationId": "create_maintenance_request_api_maintenance_request_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Maintenance Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Create Maintenance Request Api Maintenance Request Post"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/maintenance/{asset_tag}/complete": {"put": {"tags": ["Maintenance"], "summary": "Complete Maintenance", "description": "Complete maintenance and automatically update asset status back to 'Ready'.\nThis follows the workflow: Maintenance completed → Asset status changes to ready.", "operationId": "complete_maintenance_api_maintenance__asset_tag__complete_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "asset_tag", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON>"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Completion Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/packing-list/pack": {"post": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Create Packing List Pack", "description": "Create a new packing list pack based on master blueprint", "operationId": "create_packing_list_pack_api_supply_workflow_packing_list_pack_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Get Packing List Packs", "description": "Get all packing list workflow records", "operationId": "get_packing_list_packs_api_supply_workflow_packing_list_pack_get", "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/packing-list/{workflow_id}/proof": {"put": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Proof Packing List", "description": "Move packing list to proof stage", "operationId": "proof_packing_list_api_supply_workflow_packing_list__workflow_id__proof_put", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProofRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/packing-list/{workflow_id}/complete": {"put": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Complete Packing List", "description": "Move packing list to complete stage - ready for transfer", "operationId": "complete_packing_list_api_supply_workflow_packing_list__workflow_id__complete_put", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/packing-list/transfer": {"post": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Create Packing List Transfer", "description": "Create transfer from completed packing list to election destination", "operationId": "create_packing_list_transfer_api_supply_workflow_packing_list_transfer_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/packing-list/{workflow_id}/unpack": {"put": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Unpack Packing List", "description": "Unpack packing list after election - update asset status and master blueprint", "operationId": "unpack_packing_list_api_supply_workflow_packing_list__workflow_id__unpack_put", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnpackRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/rolling-cage/pack": {"post": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Create Rolling Cage Pack", "description": "Create a new rolling cage pack based on master blueprint", "operationId": "create_rolling_cage_pack_api_supply_workflow_rolling_cage_pack_post", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackCreateRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Get Rolling Cage Packs", "description": "Get all rolling cage workflow records", "operationId": "get_rolling_cage_packs_api_supply_workflow_rolling_cage_pack_get", "parameters": [{"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackListResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/rolling-cage/{workflow_id}/proof": {"put": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Proof Rolling Cage", "description": "Move rolling cage to proof stage", "operationId": "proof_rolling_cage_api_supply_workflow_rolling_cage__workflow_id__proof_put", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProofRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/rolling-cage/{workflow_id}/complete": {"put": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Complete Rolling Cage", "description": "Move rolling cage to complete stage - ready for transfer", "operationId": "complete_rolling_cage_api_supply_workflow_rolling_cage__workflow_id__complete_put", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompleteRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/rolling-cage/transfer": {"post": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Create Rolling Cage Transfer", "description": "Create transfer record for completed rolling cage", "operationId": "create_rolling_cage_transfer_api_supply_workflow_rolling_cage_transfer_post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TransferCreateRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/rolling-cage/{workflow_id}/unpack": {"put": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Unpack Rolling Cage", "description": "Unpack rolling cage after election - update asset statuses and master", "operationId": "unpack_rolling_cage_api_supply_workflow_rolling_cage__workflow_id__unpack_put", "parameters": [{"name": "workflow_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Workflow Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnpackRequest"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PackOperationResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/master-blueprints": {"get": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Get Master Blueprints", "description": "Get master blueprints for dropdown selection in pack creation", "operationId": "get_master_blueprints_api_supply_workflow_master_blueprints_get", "parameters": [{"name": "blueprint_type", "in": "query", "required": true, "schema": {"type": "string", "description": "packing_list or rolling_cage", "title": "Blueprint Type"}, "description": "packing_list or rolling_cage"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MasterBlueprintResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/supply-workflow/dropdown-data": {"get": {"tags": ["Supply Workflow - Pack/Proof/Complete/Unpack"], "summary": "Get Dropdown Data", "description": "Get all dropdown data needed for pack creation forms", "operationId": "get_dropdown_data_api_supply_workflow_dropdown_data_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "object", "title": "Response Get Dropdown Data Api Supply Workflow Dropdown Data Get"}}}}}}}, "/api/packing-page/": {"get": {"tags": ["Packing Page Records"], "summary": "Get Packing Records", "description": "Get all packing records with optional filtering.\nEquivalent to GET / in Node.js packingpage.ts", "operationId": "get_packing_records_api_packing_page__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "default": 1, "title": "Page"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "default": 50, "title": "Limit"}}, {"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Search"}}, {"name": "election", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"$ref": "#/components/schemas/PackingStatus"}, {"type": "null"}], "title": "Status"}}, {"name": "packingUser", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Packinguser"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Contactperson"}}, {"name": "startDate", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Startdate"}}, {"name": "endDate", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Enddate"}}, {"name": "deliveryStartDate", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deliverystartdate"}}, {"name": "deliveryEndDate", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Deliveryenddate"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Packing Page Records"], "summary": "Create Packing Record", "description": "Create new packing record.\nEquivalent to POST / in Node.js packingpage.ts", "operationId": "create_packing_record_api_packing_page__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Packing Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-page/stats": {"get": {"tags": ["Packing Page Records"], "summary": "Get Packing Stats", "description": "Get packing dashboard statistics.\nEquivalent to GET /stats in Node.js packingpage.ts", "operationId": "get_packing_stats_api_packing_page_stats_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/packing-page/verification-status": {"get": {"tags": ["Packing Page Records"], "summary": "Get Verification Status", "description": "Get verification status for packages.\nEquivalent to GET /verification-status in Node.js packingpage.ts", "operationId": "get_verification_status_api_packing_page_verification_status_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}, {"name": "package", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Package"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-page/{record_id}": {"get": {"tags": ["Packing Page Records"], "summary": "Get Packing Record", "description": "Get specific packing record by ID.\nEquivalent to GET /:id in Node.js packingpage.ts", "operationId": "get_packing_record_api_packing_page__record_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Record Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Packing Page Records"], "summary": "Update Packing Record", "description": "Update packing record.\nEquivalent to PUT /:id in Node.js packingpage.ts", "operationId": "update_packing_record_api_packing_page__record_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Record Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Packing Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Packing Page Records"], "summary": "Delete Packing Record", "description": "Delete packing record.\nEquivalent to DELETE /:id in Node.js packingpage.ts", "operationId": "delete_packing_record_api_packing_page__record_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Record Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-page/{record_id}/status": {"put": {"tags": ["Packing Page Records"], "summary": "Update Packing Status", "description": "Update packing status.\nEquivalent to PUT /:id/status in Node.js packingpage.ts", "operationId": "update_packing_status_api_packing_page__record_id__status_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Record Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Status Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-page/{record_id}/unpack": {"put": {"tags": ["Packing Page Records"], "summary": "Update Unpacking Info", "description": "Update unpacking information.\nEquivalent to PUT /:id/unpack in Node.js packingpage.ts", "operationId": "update_unpacking_info_api_packing_page__record_id__unpack_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Record Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Unpack Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-page/{record_id}/verify": {"put": {"tags": ["Packing Page Records"], "summary": "Verify Packing Completion", "description": "Verify packing completion.\nEquivalent to PUT /:id/verify in Node.js packingpage.ts", "operationId": "verify_packing_completion_api_packing_page__record_id__verify_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "record_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Record Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Verify Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-page/elections/list": {"get": {"tags": ["Packing Page Records"], "summary": "Get Elections List", "description": "Get unique elections.", "operationId": "get_elections_list_api_packing_page_elections_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/packing-page/locations/list": {"get": {"tags": ["Packing Page Records"], "summary": "Get Locations List", "description": "Get unique locations.", "operationId": "get_locations_list_api_packing_page_locations_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/packing-page/packages/list": {"get": {"tags": ["Packing Page Records"], "summary": "Get Packages List", "description": "Get unique packages.", "operationId": "get_packages_list_api_packing_page_packages_list_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "election", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election"}}, {"name": "location", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-page/users/list": {"get": {"tags": ["Packing Page Records"], "summary": "Get Users List", "description": "Get unique users.", "operationId": "get_users_list_api_packing_page_users_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/packing-lists/": {"get": {"tags": ["Packing Lists"], "summary": "Get Packing Lists", "description": "Get all master packing list blueprints.\nReturns data in the format expected by the frontend.", "operationId": "get_packing_lists_api_packing_lists__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}, "post": {"tags": ["Packing Lists"], "summary": "Create Packing List", "description": "Create new master packing list blueprint.\nAuto-generates ref_no in PL#### format.", "operationId": "create_packing_list_api_packing_lists__post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Packing List Data"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/packing-lists/{list_id}": {"get": {"tags": ["Packing Lists"], "summary": "Get Packing List", "description": "Get master packing list blueprint by ID.", "operationId": "get_packing_list_api_packing_lists__list_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Packing Lists"], "summary": "Update Packing List", "description": "Update packing list.\nEquivalent to PUT /:id in Node.js packingList.ts", "operationId": "update_packing_list_api_packing_lists__list_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Packing List Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Packing Lists"], "summary": "Delete Packing List", "description": "Delete packing list.\nEquivalent to DELETE /:id in Node.js packingList.ts", "operationId": "delete_packing_list_api_packing_lists__list_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-lists/{list_id}/verification": {"get": {"tags": ["Packing Lists"], "summary": "Get Verification Items", "description": "Get verification items for a packing list.\nEquivalent to GET /:id/verification in Node.js packingList.ts", "operationId": "get_verification_items_api_packing_lists__list_id__verification_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Packing Lists"], "summary": "Add Verification Item", "description": "Add verification item.\nEquivalent to POST /:id/verification in Node.js packingList.ts", "operationId": "add_verification_item_api_packing_lists__list_id__verification_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Verification Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-lists/{list_id}/verification/{verification_id}": {"put": {"tags": ["Packing Lists"], "summary": "Update Verification Item", "description": "Update verification item.\nEquivalent to PUT /:id/verification/:verificationId in Node.js packingList.ts", "operationId": "update_verification_item_api_packing_lists__list_id__verification__verification_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}, {"name": "verification_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Verification Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Verification Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Packing Lists"], "summary": "Delete Verification Item", "description": "Delete verification item (Undo action).\nEquivalent to DELETE /:id/verification/:verificationId in Node.js packingList.ts", "operationId": "delete_verification_item_api_packing_lists__list_id__verification__verification_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}, {"name": "verification_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Verification Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-lists/{list_id}/completion": {"get": {"tags": ["Packing Lists"], "summary": "Get Completion Status", "description": "Get completion status.\nEquivalent to GET /:id/completion in Node.js packingList.ts", "operationId": "get_completion_status_api_packing_lists__list_id__completion_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Packing Lists"], "summary": "Update Completion Status", "description": "Update completion status.\nEquivalent to PUT /:id/completion in Node.js packingList.ts", "operationId": "update_completion_status_api_packing_lists__list_id__completion_put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Completion Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-lists/{list_id}/unpack": {"get": {"tags": ["Packing Lists"], "summary": "Get Unpack Items", "description": "Get unpack items for a packing list.\nEquivalent to GET /:id/unpack in Node.js packingList.ts", "operationId": "get_unpack_items_api_packing_lists__list_id__unpack_get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Packing Lists"], "summary": "Add Unpack Item", "description": "Add unpack item.\nEquivalent to POST /:id/unpack in Node.js packingList.ts", "operationId": "add_unpack_item_api_packing_lists__list_id__unpack_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Unpack Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-lists/{list_id}/unpack/{unpack_id}": {"put": {"tags": ["Packing Lists"], "summary": "Update Unpack Item", "description": "Update unpack item.\nEquivalent to PUT /:id/unpack/:unpackId in Node.js packingList.ts", "operationId": "update_unpack_item_api_packing_lists__list_id__unpack__unpack_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}, {"name": "unpack_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Unpack Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Unpack Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Packing Lists"], "summary": "Delete Unpack Item", "description": "Delete unpack item.\nEquivalent to DELETE /:id/unpack/:unpackId in Node.js packingList.ts", "operationId": "delete_unpack_item_api_packing_lists__list_id__unpack__unpack_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}, {"name": "unpack_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Unpack Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-lists/{list_id}/unpack/{unpack_id}/quantity": {"patch": {"tags": ["Packing Lists"], "summary": "Update Unpack Quantity", "description": "Bulk update unpack quantities (for + and - buttons).\nEquivalent to PATCH /:id/unpack/:unpackId/quantity in Node.js packingList.ts", "operationId": "update_unpack_quantity_api_packing_lists__list_id__unpack__unpack_id__quantity_patch", "security": [{"HTTPBearer": []}], "parameters": [{"name": "list_id", "in": "path", "required": true, "schema": {"type": "string", "title": "List Id"}}, {"name": "unpack_id", "in": "path", "required": true, "schema": {"type": "string", "title": "Unpack Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Quantity Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/packing-lists/config/items": {"get": {"tags": ["Packing Lists"], "summary": "Get Items Dropdown", "description": "Get items for dropdown in packing list creation/editing.\nReturns consumables from masters_consumables table.", "operationId": "get_items_dropdown_api_packing_lists_config_items_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/packing-lists/config/locations": {"get": {"tags": ["Packing Lists"], "summary": "Get Locations Dropdown", "description": "Get locations for dropdown in packing list creation/editing.\nReturns locations from masters_locations and packing_locations tables.", "operationId": "get_locations_dropdown_api_packing_lists_config_locations_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/masters/packing-lists/": {"get": {"tags": ["Master Packing List Blueprints"], "summary": "Get Master Packing Lists", "description": "Get all master packing list blueprints with optional filtering and pagination.", "operationId": "get_master_packing_lists_api_masters_packing_lists__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "search", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search in description or ref_no", "title": "Search"}, "description": "Search in description or ref_no"}, {"name": "status", "in": "query", "required": false, "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by status (true=ON, false=OFF)", "title": "Status"}, "description": "Filter by status (true=ON, false=OFF)"}, {"name": "page", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 1, "description": "Page number", "default": 1, "title": "Page"}, "description": "Page number"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 100, "minimum": 1, "description": "Items per page", "default": 10, "title": "Limit"}, "description": "Items per page"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "post": {"tags": ["Master Packing List Blueprints"], "summary": "Create Master Packing List", "description": "Create a new master packing list blueprint.\nAuto-generates ref_no in PL#### format.", "operationId": "create_master_packing_list_api_masters_packing_lists__post", "security": [{"HTTPBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Packing List Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/packing-lists/{packing_list_id}": {"get": {"tags": ["Master Packing List Blueprints"], "summary": "Get Master Packing List", "description": "Get a specific master packing list blueprint by ID.", "operationId": "get_master_packing_list_api_masters_packing_lists__packing_list_id__get", "security": [{"HTTPBearer": []}], "parameters": [{"name": "packing_list_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Packing List Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Master Packing List Blueprints"], "summary": "Update Master Packing List", "description": "Update a master packing list blueprint.", "operationId": "update_master_packing_list_api_masters_packing_lists__packing_list_id__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "packing_list_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Packing List Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Packing List Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Master Packing List Blueprints"], "summary": "Delete Master Packing List", "description": "Delete a master packing list blueprint.", "operationId": "delete_master_packing_list_api_masters_packing_lists__packing_list_id__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "packing_list_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Packing List Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/packing-lists/{packing_list_id}/items": {"post": {"tags": ["Master Packing List Blueprints"], "summary": "Add Item To Packing List", "description": "Add an item to a master packing list blueprint.", "operationId": "add_item_to_packing_list_api_masters_packing_lists__packing_list_id__items_post", "security": [{"HTTPBearer": []}], "parameters": [{"name": "packing_list_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Packing List Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Item Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/packing-lists/{packing_list_id}/items/{item_no}": {"put": {"tags": ["Master Packing List Blueprints"], "summary": "Update Item In Packing List", "description": "Update an item in a master packing list blueprint.", "operationId": "update_item_in_packing_list_api_masters_packing_lists__packing_list_id__items__item_no__put", "security": [{"HTTPBearer": []}], "parameters": [{"name": "packing_list_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Packing List Id"}}, {"name": "item_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Item No"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "object", "title": "Item Data"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Master Packing List Blueprints"], "summary": "Remove Item From Packing List", "description": "Remove an item from a master packing list blueprint.", "operationId": "remove_item_from_packing_list_api_masters_packing_lists__packing_list_id__items__item_no__delete", "security": [{"HTTPBearer": []}], "parameters": [{"name": "packing_list_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Packing List Id"}}, {"name": "item_no", "in": "path", "required": true, "schema": {"type": "string", "title": "Item No"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/masters/packing-lists/config/items": {"get": {"tags": ["Master Packing List Blueprints"], "summary": "Get Items Dropdown", "description": "Get items for dropdown in packing list creation/editing.\nReturns consumables from masters_consumables table.", "operationId": "get_items_dropdown_api_masters_packing_lists_config_items_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/masters/packing-lists/config/locations": {"get": {"tags": ["Master Packing List Blueprints"], "summary": "Get Locations Dropdown", "description": "Get locations for dropdown in packing list creation/editing.\nReturns locations matching the screenshots.", "operationId": "get_locations_dropdown_api_masters_packing_lists_config_locations_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}, "/api/masters/packing-lists/config/next-ref-no": {"get": {"tags": ["Master Packing List Blueprints"], "summary": "Get Next Ref No", "description": "Get the next available ref number for auto-filling the form.", "operationId": "get_next_ref_no_api_masters_packing_lists_config_next_ref_no_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"HTTPBearer": []}]}}}, "components": {"schemas": {"AssetCondition": {"type": "string", "enum": ["Excellent", "Good", "Fair", "Poor", "Damaged"], "title": "AssetCondition"}, "AssetCreate": {"properties": {"asset_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Asset Id"}, "type": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Type"}, "model": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Model"}, "serial_number": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Serial Number"}, "status": {"anyOf": [{"$ref": "#/components/schemas/AssetStatus"}, {"type": "null"}], "default": "New"}, "condition": {"anyOf": [{"$ref": "#/components/schemas/AssetCondition"}, {"type": "null"}], "default": "Good"}, "location": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Location"}, "assigned_to": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Assigned To"}, "state": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "State"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date"}, "warranty_expiry": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON>ranty Expiry"}, "last_maintenance": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Maintenance"}, "next_maintenance": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Maintenance"}, "last_checked": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Checked"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "specifications": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Specifications"}}, "type": "object", "required": ["type", "location"], "title": "AssetCreate"}, "AssetStatus": {"type": "string", "enum": ["New", "Ready", "Failed", "Packed", "Checked-out", "In-transfer", "Delivered", "Using", "Damaged", "Under Maintenance", "Completed", "Retired"], "title": "AssetStatus"}, "AssetStatusChangeRequest": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id"}, "new_status": {"type": "string", "title": "New Status"}, "change_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Change Reason"}, "workflow_module": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Module"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["asset_id", "new_status"], "title": "AssetStatusChangeRequest"}, "AssetStatusTransitionRequest": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the asset"}, "to_status": {"type": "string", "title": "To Status", "description": "Target status"}, "workflow_module": {"allOf": [{"$ref": "#/components/schemas/WorkflowModuleEnum"}], "description": "Workflow module triggering the transition"}, "change_reason": {"type": "string", "title": "Change Reason", "description": "Reason for status change"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id", "description": "Session ID for tracking"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Additional notes"}, "context": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Context", "description": "Additional context data"}}, "type": "object", "required": ["asset_id", "to_status", "workflow_module", "change_reason"], "title": "AssetStatusTransitionRequest"}, "AssetStatusTransitionResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "asset_id": {"type": "integer", "title": "Asset Id"}, "previous_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Previous Status"}, "new_status": {"type": "string", "title": "New Status"}, "changed_by": {"type": "integer", "title": "Changed By"}, "changed_at": {"type": "string", "format": "date-time", "title": "Changed At"}}, "type": "object", "required": ["success", "message", "asset_id", "previous_status", "new_status", "changed_by", "changed_at"], "title": "AssetStatusTransitionResponse"}, "AssetStatusUpdate": {"properties": {"status": {"$ref": "#/components/schemas/AssetStatus"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "assigned_to": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["status"], "title": "AssetStatusUpdate"}, "AssetTransferCreate": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the asset being transferred"}, "transfer_type": {"allOf": [{"$ref": "#/components/schemas/TransferTypeEnum"}], "description": "Type of transfer"}, "from_location": {"type": "string", "maxLength": 255, "title": "From Location", "description": "Origin location"}, "to_location": {"type": "string", "maxLength": 255, "title": "To Location", "description": "Destination location"}, "from_county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "From County", "description": "Origin county"}, "to_county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "To County", "description": "Destination county"}, "from_precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "From Precinct", "description": "Origin precinct"}, "to_precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "To Precinct", "description": "Destination precinct"}, "assigned_to": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Assigned To", "description": "Personnel receiving asset"}, "assigned_email": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Assigned Email", "description": "Recipient email"}, "assigned_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Assigned Phone", "description": "Recipient phone"}, "expected_delivery_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Delivery Date", "description": "Expected delivery date"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Transfer notes"}, "special_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Instructions", "description": "Special handling instructions"}}, "type": "object", "required": ["asset_id", "transfer_type", "from_location", "to_location"], "title": "AssetTransferCreate"}, "AssetTransferResponse": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the asset being transferred"}, "transfer_type": {"allOf": [{"$ref": "#/components/schemas/TransferTypeEnum"}], "description": "Type of transfer"}, "from_location": {"type": "string", "maxLength": 255, "title": "From Location", "description": "Origin location"}, "to_location": {"type": "string", "maxLength": 255, "title": "To Location", "description": "Destination location"}, "from_county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "From County", "description": "Origin county"}, "to_county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "To County", "description": "Destination county"}, "from_precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "From Precinct", "description": "Origin precinct"}, "to_precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "To Precinct", "description": "Destination precinct"}, "assigned_to": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Assigned To", "description": "Personnel receiving asset"}, "assigned_email": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Assigned Email", "description": "Recipient email"}, "assigned_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Assigned Phone", "description": "Recipient phone"}, "expected_delivery_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Delivery Date", "description": "Expected delivery date"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes", "description": "Transfer notes"}, "special_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Instructions", "description": "Special handling instructions"}, "id": {"type": "integer", "title": "Id"}, "transfer_id": {"type": "string", "title": "Transfer Id"}, "status": {"$ref": "#/components/schemas/TransferStatusEnum"}, "tracking_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tracking Number"}, "carrier": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Carrier"}, "actual_delivery_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Delivery Date"}, "verification_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Verification Code"}, "verified_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Verified By"}, "verified_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Verified At"}, "condition_before_transfer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Condition Before Transfer"}, "condition_after_transfer": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Condition After Transfer"}, "initiated_by": {"type": "integer", "title": "Initiated By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}}, "type": "object", "required": ["asset_id", "transfer_type", "from_location", "to_location", "id", "transfer_id", "status", "tracking_number", "carrier", "actual_delivery_date", "verification_code", "verified_by", "verified_at", "condition_before_transfer", "condition_after_transfer", "initiated_by", "created_at", "updated_at", "completed_at"], "title": "AssetTransferResponse"}, "AssetTransferUpdate": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/TransferStatusEnum"}, {"type": "null"}]}, "tracking_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Tracking Number"}, "carrier": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Carrier"}, "expected_delivery_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Delivery Date"}, "actual_delivery_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Delivery Date"}, "verification_code": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Verification Code"}, "condition_after_transfer": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Condition After Transfer"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "title": "AssetTransferUpdate"}, "AssetUpdate": {"properties": {"asset_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Asset Id"}, "type": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Type"}, "model": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Model"}, "serial_number": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Serial Number"}, "status": {"anyOf": [{"$ref": "#/components/schemas/AssetStatus"}, {"type": "null"}]}, "condition": {"anyOf": [{"$ref": "#/components/schemas/AssetCondition"}, {"type": "null"}]}, "location": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Location"}, "assigned_to": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Assigned To"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct"}, "purchase_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Purchase Date"}, "warranty_expiry": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "<PERSON>ranty Expiry"}, "last_maintenance": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Maintenance"}, "next_maintenance": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Next Maintenance"}, "last_checked": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Checked"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "specifications": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Specifications"}}, "type": "object", "title": "AssetUpdate"}, "Body_upload_election_file_api_elections__election_id__files_post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File"}}, "type": "object", "required": ["file"], "title": "Body_upload_election_file_api_elections__election_id__files_post"}, "CheckoutCreate": {"properties": {"from_location_id": {"type": "string", "title": "From Location Id"}, "from_location_type": {"type": "string", "title": "From Location Type", "default": "packing"}, "to_location_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To Location Id"}, "to_location_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To Location Type"}, "to_user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To User Id"}, "is_location_checkout": {"type": "boolean", "title": "Is Location Checkout", "default": true}, "checkout_date": {"type": "string", "format": "date", "title": "Checkout Date"}, "election_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election Id"}, "is_stock_distribution": {"type": "boolean", "title": "Is Stock Distribution", "default": false}, "workflow_stage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Stage"}, "workflow_reference_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Reference Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "items": {"items": {"$ref": "#/components/schemas/CheckoutItemBase"}, "type": "array", "title": "Items", "default": []}}, "type": "object", "required": ["from_location_id", "checkout_date"], "title": "CheckoutCreate"}, "CheckoutItemBase": {"properties": {"consumable_id": {"type": "string", "title": "Consumable Id"}, "quantity": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Quantity"}}, "type": "object", "required": ["consumable_id", "quantity"], "title": "CheckoutItemBase"}, "CheckoutSessionCreate": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the asset being checked out"}, "checkout_type": {"allOf": [{"$ref": "#/components/schemas/CheckoutTypeEnum"}], "description": "Type of checkout"}, "personnel_name": {"type": "string", "maxLength": 255, "title": "Personnel Name", "description": "Name of person checking out asset"}, "personnel_email": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Personnel Email", "description": "Email of person checking out asset"}, "personnel_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Personnel Phone", "description": "Phone of person checking out asset"}, "personnel_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Personnel Id", "description": "Employee/Vendor ID"}, "from_location": {"type": "string", "maxLength": 255, "title": "From Location", "description": "Checkout location"}, "to_location": {"type": "string", "maxLength": 255, "title": "To Location", "description": "Deployment location"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County", "description": "County"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct", "description": "Precinct"}, "purpose": {"type": "string", "title": "Purpose", "description": "Purpose of checkout"}, "expected_return_date": {"type": "string", "format": "date-time", "title": "Expected Return Date", "description": "Expected return date"}, "condition_at_checkout": {"type": "string", "maxLength": 50, "title": "Condition At Checkout", "description": "Asset condition at checkout"}, "checkout_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Checkout Notes", "description": "Checkout notes"}, "authorized_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Authorized By", "description": "User ID who authorized checkout"}, "authorization_code": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Authorization Code", "description": "Authorization code"}}, "type": "object", "required": ["asset_id", "checkout_type", "personnel_name", "from_location", "to_location", "purpose", "expected_return_date", "condition_at_checkout"], "title": "CheckoutSessionCreate"}, "CheckoutSessionResponse": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the asset being checked out"}, "checkout_type": {"allOf": [{"$ref": "#/components/schemas/CheckoutTypeEnum"}], "description": "Type of checkout"}, "personnel_name": {"type": "string", "maxLength": 255, "title": "Personnel Name", "description": "Name of person checking out asset"}, "personnel_email": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Personnel Email", "description": "Email of person checking out asset"}, "personnel_phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Personnel Phone", "description": "Phone of person checking out asset"}, "personnel_id": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Personnel Id", "description": "Employee/Vendor ID"}, "from_location": {"type": "string", "maxLength": 255, "title": "From Location", "description": "Checkout location"}, "to_location": {"type": "string", "maxLength": 255, "title": "To Location", "description": "Deployment location"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County", "description": "County"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct", "description": "Precinct"}, "purpose": {"type": "string", "title": "Purpose", "description": "Purpose of checkout"}, "expected_return_date": {"type": "string", "format": "date-time", "title": "Expected Return Date", "description": "Expected return date"}, "condition_at_checkout": {"type": "string", "maxLength": 50, "title": "Condition At Checkout", "description": "Asset condition at checkout"}, "checkout_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Checkout Notes", "description": "Checkout notes"}, "id": {"type": "integer", "title": "Id"}, "checkout_id": {"type": "string", "title": "Checkout Id"}, "status": {"$ref": "#/components/schemas/CheckoutStatusEnum"}, "checked_out_by": {"type": "integer", "title": "Checked Out By"}, "authorized_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Authorized By"}, "authorization_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Authorization Code"}, "actual_return_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Return Date"}, "checkout_date": {"type": "string", "format": "date-time", "title": "Checkout Date"}, "condition_at_return": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Condition At Return"}, "returned_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Returned By"}, "return_verified_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Return Verified By"}, "return_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Return Notes"}, "extension_count": {"type": "integer", "title": "Extension Count"}, "extension_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Extension Reason"}, "last_extension_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Extension Date"}, "requires_special_handling": {"type": "boolean", "title": "Requires Special Handling"}, "special_instructions": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Special Instructions"}, "reminder_sent_count": {"type": "integer", "title": "Reminder <PERSON>"}, "last_reminder_sent": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Last Reminder <PERSON>"}, "internal_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Internal Notes"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}}, "type": "object", "required": ["asset_id", "checkout_type", "personnel_name", "from_location", "to_location", "purpose", "expected_return_date", "condition_at_checkout", "id", "checkout_id", "status", "checked_out_by", "authorized_by", "authorization_code", "actual_return_date", "checkout_date", "condition_at_return", "returned_by", "return_verified_by", "return_notes", "extension_count", "extension_reason", "last_extension_date", "requires_special_handling", "special_instructions", "reminder_sent_count", "last_reminder_sent", "internal_notes", "created_at", "updated_at", "completed_at"], "title": "CheckoutSessionResponse"}, "CheckoutSessionUpdate": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/CheckoutStatusEnum"}, {"type": "null"}]}, "actual_return_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Actual Return Date"}, "condition_at_return": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Condition At Return"}, "return_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Return Notes"}, "returned_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Returned By"}, "return_verified_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Return Verified By"}, "extension_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Extension Reason"}, "expected_return_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Expected Return Date"}}, "type": "object", "title": "CheckoutSessionUpdate"}, "CheckoutStatus": {"type": "string", "enum": ["Active", "Completed", "Overdue", "Cancelled", "Extended"], "title": "CheckoutStatus"}, "CheckoutStatusEnum": {"type": "string", "enum": ["Active", "Completed", "Overdue", "Cancelled", "Extended"], "title": "CheckoutStatusEnum"}, "CheckoutStatusUpdate": {"properties": {"status": {"type": "string", "pattern": "^(pending|completed|cancelled)$", "title": "Status"}}, "type": "object", "required": ["status"], "title": "CheckoutStatusUpdate"}, "CheckoutType": {"type": "string", "enum": ["Deployment", "Maintenance", "Testing", "Training", "Emergency", "Temporary"], "title": "CheckoutType"}, "CheckoutTypeEnum": {"type": "string", "enum": ["Deployment", "Maintenance", "Testing", "Training", "Emergency", "Temporary"], "title": "CheckoutTypeEnum"}, "CheckoutUpdate": {"properties": {"from_location_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "From Location Id"}, "from_location_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "From Location Type"}, "to_location_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To Location Id"}, "to_location_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To Location Type"}, "to_user_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "To User Id"}, "is_location_checkout": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Location Checkout"}, "checkout_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Checkout Date"}, "election_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election Id"}, "is_stock_distribution": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Stock Distribution"}, "workflow_stage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Stage"}, "workflow_reference_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Reference Id"}, "status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Status"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "title": "CheckoutUpdate"}, "CompleteRequest": {"properties": {"completed_by": {"type": "string", "title": "Completed By"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["completed_by"], "title": "CompleteRequest"}, "ConsumableCreate": {"properties": {"item_no": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Item No"}, "name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "category_id": {"type": "string", "title": "Category Id"}, "unit": {"type": "string", "maxLength": 50, "title": "Unit", "default": "pieces"}, "min_qty": {"type": "integer", "minimum": 0.0, "title": "<PERSON>", "default": 0}, "max_qty": {"type": "integer", "minimum": 0.0, "title": "<PERSON>", "default": 1000}, "unit_cost": {"anyOf": [{"type": "number", "minimum": 0.0}, {"type": "string"}], "title": "Unit Cost", "default": 0.0}, "vendor": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "status": {"type": "boolean", "title": "Status", "default": true}, "is_critical": {"type": "boolean", "title": "Is Critical", "default": false}, "lead_time_days": {"type": "integer", "minimum": 0.0, "title": "Lead Time Days", "default": 7}, "properties": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Properties", "default": {}}}, "type": "object", "required": ["item_no", "name", "category_id"], "title": "ConsumableCreate"}, "ConsumableUpdate": {"properties": {"item_no": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Item No"}, "name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "category_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Category Id"}, "unit": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Unit"}, "min_qty": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>"}, "max_qty": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>"}, "unit_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Unit Cost"}, "vendor": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "status": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}, "is_critical": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Critical"}, "lead_time_days": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Lead Time Days"}, "properties": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Properties"}}, "type": "object", "title": "ConsumableUpdate"}, "DamageReportCreate": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the damaged asset"}, "damage_type": {"allOf": [{"$ref": "#/components/schemas/DamageTypeEnum"}], "description": "Type of damage"}, "damage_severity": {"allOf": [{"$ref": "#/components/schemas/DamageSeverityEnum"}], "description": "Severity of damage"}, "incident_date": {"type": "string", "format": "date-time", "title": "Incident Date", "description": "When damage was discovered"}, "discovered_by": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Discovered By", "description": "Person who discovered damage"}, "incident_location": {"type": "string", "maxLength": 255, "title": "Incident Location", "description": "Where damage occurred"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County", "description": "County where incident occurred"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct", "description": "Precinct where incident occurred"}, "damage_description": {"type": "string", "title": "Damage Description", "description": "Detailed description of damage"}, "cause_of_damage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cause Of Damage", "description": "Suspected cause of damage"}, "immediate_action_taken": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Immediate Action Taken", "description": "Immediate actions taken"}, "priority": {"type": "string", "maxLength": 20, "title": "Priority", "description": "Priority level", "default": "Medium"}}, "type": "object", "required": ["asset_id", "damage_type", "damage_severity", "incident_date", "incident_location", "damage_description"], "title": "DamageReportCreate"}, "DamageReportResponse": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the damaged asset"}, "damage_type": {"allOf": [{"$ref": "#/components/schemas/DamageTypeEnum"}], "description": "Type of damage"}, "damage_severity": {"allOf": [{"$ref": "#/components/schemas/DamageSeverityEnum"}], "description": "Severity of damage"}, "incident_date": {"type": "string", "format": "date-time", "title": "Incident Date", "description": "When damage was discovered"}, "discovered_by": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Discovered By", "description": "Person who discovered damage"}, "incident_location": {"type": "string", "maxLength": 255, "title": "Incident Location", "description": "Where damage occurred"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County", "description": "County where incident occurred"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct", "description": "Precinct where incident occurred"}, "damage_description": {"type": "string", "title": "Damage Description", "description": "Detailed description of damage"}, "cause_of_damage": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cause Of Damage", "description": "Suspected cause of damage"}, "immediate_action_taken": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Immediate Action Taken", "description": "Immediate actions taken"}, "priority": {"type": "string", "maxLength": 20, "title": "Priority", "description": "Priority level", "default": "Medium"}, "id": {"type": "integer", "title": "Id"}, "report_id": {"type": "string", "title": "Report Id"}, "status": {"$ref": "#/components/schemas/DamageReportStatusEnum"}, "reported_by": {"type": "integer", "title": "Reported By"}, "is_repairable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Repairable"}, "estimated_repair_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Repair Cost"}, "estimated_repair_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Estimated Repair Time"}, "warranty_covered": {"type": "boolean", "title": "Warranty Covered"}, "insurance_claim_needed": {"type": "boolean", "title": "Insurance Claim Needed"}, "insurance_claim_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Insurance Claim Number"}, "assigned_to": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assigned To"}, "resolution_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Resolution Notes"}, "repair_vendor": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "actual_repair_cost": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Actual Repair Cost"}, "reported_at": {"type": "string", "format": "date-time", "title": "Reported At"}, "reviewed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Reviewed At"}, "approved_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Approved At"}, "repair_started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Started At"}, "repair_completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Repair Completed At"}, "closed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Closed At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["asset_id", "damage_type", "damage_severity", "incident_date", "incident_location", "damage_description", "id", "report_id", "status", "reported_by", "is_repairable", "estimated_repair_cost", "estimated_repair_time", "warranty_covered", "insurance_claim_needed", "insurance_claim_number", "assigned_to", "resolution_notes", "repair_vendor", "actual_repair_cost", "reported_at", "reviewed_at", "approved_at", "repair_started_at", "repair_completed_at", "closed_at", "created_at", "updated_at"], "title": "DamageReportResponse"}, "DamageReportStatus": {"type": "string", "enum": ["Reported", "Under Review", "Approved", "Repair Scheduled", "Repair In Progress", "Repair Completed", "Replacement Ordered", "Closed", "Rejected"], "title": "DamageReportStatus"}, "DamageReportStatusEnum": {"type": "string", "enum": ["Reported", "Under Review", "Approved", "Repair Scheduled", "Repair In Progress", "Repair Completed", "Replacement Ordered", "Closed", "Rejected"], "title": "DamageReportStatusEnum"}, "DamageReportUpdate": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/DamageReportStatusEnum"}, {"type": "null"}]}, "is_repairable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Repairable"}, "estimated_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Estimated Repair Cost"}, "estimated_repair_time": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Estimated Repair Time"}, "warranty_covered": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Warranty Covered"}, "insurance_claim_needed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Insurance Claim Needed"}, "insurance_claim_number": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Insurance Claim Number"}, "assigned_to": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Assigned To"}, "resolution_notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Resolution Notes"}, "repair_vendor": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "<PERSON><PERSON>"}, "actual_repair_cost": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "title": "Actual Repair Cost"}}, "type": "object", "title": "DamageReportUpdate"}, "DamageSeverity": {"type": "string", "enum": ["Minor", "Moderate", "Major", "Critical", "Total Loss"], "title": "DamageSeverity"}, "DamageSeverityEnum": {"type": "string", "enum": ["Minor", "Moderate", "Major", "Critical", "Total Loss"], "title": "DamageSeverityEnum"}, "DamageType": {"type": "string", "enum": ["Physical", "Functional", "Cosmetic", "Electrical", "Software", "Water", "Other"], "title": "DamageType"}, "DamageTypeEnum": {"type": "string", "enum": ["Physical", "Functional", "Cosmetic", "Electrical", "Software", "Water", "Other"], "title": "DamageTypeEnum"}, "ElectionCreate": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name", "description": "Election name"}, "election_type_id": {"type": "string", "title": "Election Type Id", "description": "Election type ID"}, "election_date": {"type": "string", "format": "date", "title": "Election Date", "description": "Election date"}, "status": {"type": "boolean", "title": "Status", "description": "Election status", "default": true}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Election description"}}, "type": "object", "required": ["name", "election_type_id", "election_date"], "title": "ElectionCreate"}, "ElectionDetailResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"$ref": "#/components/schemas/ElectionResponse"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}}, "type": "object", "required": ["success", "data"], "title": "ElectionDetailResponse"}, "ElectionListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"type": "object", "title": "Data"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}}, "type": "object", "required": ["success", "data"], "title": "ElectionListResponse"}, "ElectionNoteCreate": {"properties": {"note_text": {"type": "string", "minLength": 1, "title": "Note Text", "description": "Note text"}}, "type": "object", "required": ["note_text"], "title": "ElectionNoteCreate"}, "ElectionResponse": {"properties": {"name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Name", "description": "Election name"}, "election_type_id": {"type": "string", "title": "Election Type Id", "description": "Election type ID"}, "election_date": {"type": "string", "format": "date", "title": "Election Date", "description": "Election date"}, "status": {"type": "boolean", "title": "Status", "description": "Election status", "default": true}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Election description"}, "id": {"type": "string", "title": "Id"}, "election_type": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Election Type"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "election_type_id", "election_date", "id", "created_at", "updated_at"], "title": "ElectionResponse"}, "ElectionUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Name", "description": "Election name"}, "election_type_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election Type Id", "description": "Election type ID"}, "election_date": {"anyOf": [{"type": "string", "format": "date"}, {"type": "null"}], "title": "Election Date", "description": "Election date"}, "status": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status", "description": "Election status"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Election description"}}, "type": "object", "title": "ElectionUpdate"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "LAChecklistItemBase": {"properties": {"item_name": {"type": "string", "title": "Item Name"}, "item_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Item Description"}, "is_required": {"type": "boolean", "title": "Is Required", "default": true}}, "type": "object", "required": ["item_name"], "title": "LAChecklistItemBase"}, "LAChecklistItemResponse": {"properties": {"item_name": {"type": "string", "title": "Item Name"}, "item_description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Item Description"}, "is_required": {"type": "boolean", "title": "Is Required", "default": true}, "id": {"type": "integer", "title": "Id"}, "session_id": {"type": "integer", "title": "Session Id"}, "status": {"$ref": "#/components/schemas/LAChecklistItemStatus"}, "checked_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Checked By"}, "comments": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Comments"}, "checked_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Checked At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["item_name", "id", "session_id", "status", "checked_by", "comments", "checked_at", "created_at", "updated_at"], "title": "LAChecklistItemResponse"}, "LAChecklistItemStatus": {"type": "string", "enum": ["Pending", "Pass", "Fail", "Skipped"], "title": "LAChecklistItemStatus"}, "LAChecklistItemUpdate": {"properties": {"status": {"anyOf": [{"$ref": "#/components/schemas/LAChecklistItemStatus"}, {"type": "null"}]}, "checked_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Checked By"}, "comments": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Comments"}, "checked_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Checked At"}}, "type": "object", "title": "LAChecklistItemUpdate"}, "LAChecklistSessionResponse": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "id": {"type": "integer", "title": "Id"}, "session_id": {"type": "string", "title": "Session Id"}, "created_by": {"type": "integer", "title": "Created By"}, "status": {"$ref": "#/components/schemas/LAChecklistSessionStatus"}, "overall_result": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Overall Result"}, "started_at": {"type": "string", "format": "date-time", "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["asset_id", "id", "session_id", "created_by", "status", "overall_result", "started_at", "completed_at", "created_at", "updated_at"], "title": "LAChecklistSessionResponse"}, "LAChecklistSessionStart": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "custom_items": {"anyOf": [{"items": {"$ref": "#/components/schemas/LAChecklistItemBase"}, "type": "array"}, {"type": "null"}], "title": "Custom Items"}}, "type": "object", "required": ["asset_id"], "title": "LAChecklistSessionStart"}, "LAChecklistSessionStatus": {"type": "string", "enum": ["Active", "Completed", "Cancelled"], "title": "LAChecklistSessionStatus"}, "LAChecklistSessionWithItems": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "id": {"type": "integer", "title": "Id"}, "session_id": {"type": "string", "title": "Session Id"}, "created_by": {"type": "integer", "title": "Created By"}, "status": {"$ref": "#/components/schemas/LAChecklistSessionStatus"}, "overall_result": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Overall Result"}, "started_at": {"type": "string", "format": "date-time", "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "checklist_items": {"items": {"$ref": "#/components/schemas/LAChecklistItemResponse"}, "type": "array", "title": "Checklist Items", "default": []}}, "type": "object", "required": ["asset_id", "id", "session_id", "created_by", "status", "overall_result", "started_at", "completed_at", "created_at", "updated_at"], "title": "LAChecklistSessionWithItems"}, "LocationCreate": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name"}, "area": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Area"}, "address": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address"}, "address_line_2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line 2"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City"}, "state": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "State"}, "zip": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Zip"}, "contact_person": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Contact Person"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "status": {"type": "boolean", "title": "Status", "default": true}, "type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Type"}, "source": {"anyOf": [{"$ref": "#/components/schemas/LocationSource"}, {"type": "null"}], "default": "masters"}, "configuration_location_id": {"anyOf": [{"type": "string", "maxLength": 36}, {"type": "null"}], "title": "Configuration Location Id"}}, "type": "object", "required": ["name"], "title": "LocationCreate"}, "LocationSource": {"type": "string", "enum": ["configuration", "masters"], "title": "LocationSource"}, "LocationUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Name"}, "area": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Area"}, "address": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address"}, "address_line_2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line 2"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City"}, "state": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "State"}, "zip": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Zip"}, "contact_person": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Contact Person"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "status": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}, "type": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Type"}, "source": {"anyOf": [{"$ref": "#/components/schemas/LocationSource"}, {"type": "null"}]}, "configuration_location_id": {"anyOf": [{"type": "string", "maxLength": 36}, {"type": "null"}], "title": "Configuration Location Id"}}, "type": "object", "title": "LocationUpdate"}, "MaintenanceCategory": {"type": "string", "enum": ["AV computers", "AV printers", "tabs", "others", "scanners", "pollpads", "monitors"], "title": "MaintenanceCategory"}, "MaintenanceStatus": {"type": "string", "enum": ["REQUESTED", "SCHEDULED", "IN_PROGRESS", "TESTING", "COMPLETED"], "title": "MaintenanceStatus"}, "MasterBlueprintResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"type": "object", "title": "Data"}}, "type": "object", "required": ["success", "data"], "title": "MasterBlueprintResponse"}, "OrderStatus": {"type": "string", "enum": ["pending", "approved", "completed", "cancelled"], "title": "OrderStatus"}, "PackAssetsRequest": {"properties": {"asset_ids": {"items": {"type": "integer"}, "type": "array", "title": "Asset Ids"}, "pack_type": {"type": "string", "title": "Pack Type", "description": "PACKING_LIST or ROLLING_CAGE"}, "container_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Container Id"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["asset_ids", "pack_type"], "title": "PackAssetsRequest"}, "PackCreateRequest": {"properties": {"master_blueprint_id": {"type": "integer", "title": "Master Blueprint Id"}, "workflow_type": {"$ref": "#/components/schemas/WorkflowType"}, "assigned_location": {"type": "string", "title": "Assigned Location"}, "packed_by": {"type": "string", "title": "Packed By"}, "items": {"items": {"$ref": "#/components/schemas/PackItemRequest"}, "type": "array", "title": "Items"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["master_blueprint_id", "workflow_type", "assigned_location", "packed_by", "items"], "title": "PackCreateRequest"}, "PackItemRequest": {"properties": {"asset_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Asset Id"}, "consumable_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Consumable Id"}, "quantity_required": {"type": "integer", "title": "Quantity Required"}, "quantity_packed": {"type": "integer", "title": "Quantity Packed"}, "location": {"type": "string", "title": "Location"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["quantity_required", "quantity_packed", "location"], "title": "PackItemRequest"}, "PackListResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "data": {"type": "object", "title": "Data"}, "total": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Total"}}, "type": "object", "required": ["success", "data"], "title": "PackListResponse"}, "PackOperationResponse": {"properties": {"success": {"type": "boolean", "title": "Success"}, "message": {"type": "string", "title": "Message"}, "data": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Data"}}, "type": "object", "required": ["success", "message"], "title": "PackOperationResponse"}, "PackingStatus": {"type": "string", "enum": ["PACKED", "IN_TRANSIT", "DELIVERED", "UNPACKED", "PENDING"], "title": "PackingStatus"}, "Priority": {"type": "string", "enum": ["high", "low", "medium", "critical"], "title": "Priority"}, "ProofRequest": {"properties": {"proofed_by": {"type": "string", "title": "Proofed By"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["proofed_by"], "title": "ProofRequest"}, "QuantityUpdate": {"properties": {"asset_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Asset Id"}, "consumable_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Consumable Id"}, "new_quantity": {"type": "integer", "title": "New Quantity"}}, "type": "object", "required": ["new_quantity"], "title": "QuantityUpdate"}, "RequestType": {"type": "string", "enum": ["preventive", "corrective", "emergency"], "title": "RequestType"}, "RollingCageBulkUpdate": {"properties": {"ids": {"items": {"type": "string"}, "type": "array", "minItems": 1, "title": "Ids"}, "update_data": {"$ref": "#/components/schemas/RollingCageBulkUpdateData"}}, "type": "object", "required": ["ids", "update_data"], "title": "RollingCageBulkUpdate"}, "RollingCageBulkUpdateData": {"properties": {"quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Quantity"}, "packed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Packed"}, "unpacked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Unpacked"}}, "type": "object", "title": "RollingCageBulkUpdateData"}, "RollingCageCreate": {"properties": {"election": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Election"}, "to": {"type": "string", "maxLength": 255, "minLength": 1, "title": "To"}, "packing_date": {"type": "string", "format": "date-time", "title": "Packing Date"}, "packing_user": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Packing User"}, "supply_package": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Supply Package"}, "type": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Type"}, "location": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Location"}, "quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Quantity"}, "packed": {"type": "boolean", "title": "Packed", "default": true}, "ref_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Ref No"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "proofed_by": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Proofed By"}, "unpacked": {"type": "boolean", "title": "Unpacked", "default": false}}, "type": "object", "required": ["election", "to", "packing_date"], "title": "RollingCageCreate"}, "RollingCageUpdate": {"properties": {"election": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Election"}, "to": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "To"}, "packing_date": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Packing Date"}, "packing_user": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Packing User"}, "supply_package": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Supply Package"}, "type": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Type"}, "location": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Location"}, "quantity": {"anyOf": [{"type": "integer", "minimum": 0.0}, {"type": "null"}], "title": "Quantity"}, "packed": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Packed"}, "ref_no": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Ref No"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "proofed_by": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Proofed By"}, "unpacked": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Unpacked"}}, "type": "object", "title": "RollingCageUpdate"}, "SupplyChecklistItemResponse": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id"}, "new_status": {"type": "string", "title": "New Status"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "id": {"type": "integer", "title": "Id"}, "checklist_id": {"type": "integer", "title": "Checklist Id"}, "previous_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Previous Status"}, "processed_by": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Processed By"}, "processed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Processed At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["asset_id", "new_status", "id", "checklist_id", "previous_status", "processed_by", "processed_at", "created_at"], "title": "SupplyChecklistItemResponse"}, "SupplyChecklistResponse": {"properties": {"checklist_type": {"$ref": "#/components/schemas/SupplyChecklistType"}, "packing_list_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Packing List Id"}, "rolling_cage_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rolling Cage Id"}, "election_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election Id"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "id": {"type": "integer", "title": "Id"}, "checklist_id": {"type": "string", "title": "Checklist Id"}, "status": {"$ref": "#/components/schemas/SupplyChecklistStatus"}, "created_by": {"type": "integer", "title": "Created By"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["checklist_type", "id", "checklist_id", "status", "created_by", "started_at", "completed_at", "created_at", "updated_at"], "title": "SupplyChecklistResponse"}, "SupplyChecklistStatus": {"type": "string", "enum": ["Pending", "In Progress", "Completed", "Cancelled"], "title": "SupplyChecklistStatus"}, "SupplyChecklistType": {"type": "string", "enum": ["Pack", "Unpack"], "title": "SupplyChecklistType"}, "SupplyChecklistWithItems": {"properties": {"checklist_type": {"$ref": "#/components/schemas/SupplyChecklistType"}, "packing_list_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Packing List Id"}, "rolling_cage_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Rolling Cage Id"}, "election_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election Id"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "id": {"type": "integer", "title": "Id"}, "checklist_id": {"type": "string", "title": "Checklist Id"}, "status": {"$ref": "#/components/schemas/SupplyChecklistStatus"}, "created_by": {"type": "integer", "title": "Created By"}, "started_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Started At"}, "completed_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Completed At"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "supply_items": {"items": {"$ref": "#/components/schemas/SupplyChecklistItemResponse"}, "type": "array", "title": "Supply Items", "default": []}}, "type": "object", "required": ["checklist_type", "id", "checklist_id", "status", "created_by", "started_at", "completed_at", "created_at", "updated_at"], "title": "SupplyChecklistWithItems"}, "TransactionOrderCreate": {"properties": {"vendor": {"type": "string", "maxLength": 255, "minLength": 1, "title": "<PERSON><PERSON><PERSON>"}, "date": {"type": "string", "format": "date", "title": "Date"}, "location": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Location"}, "note": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Note"}, "items": {"items": {"$ref": "#/components/schemas/TransactionOrderItemBase"}, "type": "array", "minItems": 1, "title": "Items"}, "consumable": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Consumable"}, "qty": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Qty"}, "status": {"anyOf": [{"$ref": "#/components/schemas/OrderStatus"}, {"type": "null"}], "default": "pending"}}, "type": "object", "required": ["vendor", "date", "location", "items", "consumable", "qty"], "title": "TransactionOrderCreate"}, "TransactionOrderItemBase": {"properties": {"id": {"type": "string", "title": "Id"}, "item": {"type": "string", "minLength": 1, "title": "<PERSON><PERSON>"}, "consumable": {"type": "string", "minLength": 1, "title": "Consumable"}, "qty": {"type": "integer", "exclusiveMinimum": 0.0, "title": "Qty"}, "unit_cost": {"type": "number", "minimum": 0.0, "title": "Unit Cost"}, "amount": {"type": "number", "minimum": 0.0, "title": "Amount"}}, "type": "object", "required": ["id", "item", "consumable", "qty", "unit_cost", "amount"], "title": "TransactionOrderItemBase"}, "TransactionOrderStatusUpdate": {"properties": {"status": {"$ref": "#/components/schemas/OrderStatus"}}, "type": "object", "required": ["status"], "title": "TransactionOrderStatusUpdate"}, "TransactionOrderUpdate": {"properties": {"vendor": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>"}, "date": {"type": "null", "title": "Date"}, "location": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Location"}, "note": {"anyOf": [{"type": "string", "maxLength": 1000}, {"type": "null"}], "title": "Note"}, "items": {"anyOf": [{"items": {"$ref": "#/components/schemas/TransactionOrderItemBase"}, "type": "array"}, {"type": "null"}], "title": "Items"}, "consumable": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Consumable"}, "qty": {"anyOf": [{"type": "integer", "exclusiveMinimum": 0.0}, {"type": "null"}], "title": "Qty"}, "status": {"anyOf": [{"$ref": "#/components/schemas/OrderStatus"}, {"type": "null"}]}}, "type": "object", "title": "TransactionOrderUpdate"}, "TransferCreateRequest": {"properties": {"pack_id": {"type": "integer", "title": "Pack Id"}, "destination_location": {"type": "string", "title": "Destination Location"}, "election_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Election Id"}, "transferred_by": {"type": "string", "title": "Transferred By"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["pack_id", "destination_location", "transferred_by"], "title": "TransferCreateRequest"}, "TransferStatus": {"type": "string", "enum": ["Initiated", "In Transit", "Delivered", "Failed", "Cancelled"], "title": "TransferStatus"}, "TransferStatusEnum": {"type": "string", "enum": ["Initiated", "In Transit", "Delivered", "Failed", "Cancelled"], "title": "TransferStatusEnum"}, "TransferType": {"type": "string", "enum": ["Deployment", "Return", "Relocation", "Maintenance"], "title": "TransferType"}, "TransferTypeEnum": {"type": "string", "enum": ["Deployment", "Return", "Relocation", "Maintenance"], "title": "TransferTypeEnum"}, "UnpackAssetsRequest": {"properties": {"container_type": {"type": "string", "title": "Container Type", "description": "PACKING_LIST or ROLLING_CAGE"}, "container_id": {"type": "integer", "title": "Container Id"}, "location": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Location"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["container_type", "container_id"], "title": "UnpackAssetsRequest"}, "UnpackRequest": {"properties": {"unpacked_by": {"type": "string", "title": "Unpacked By"}, "return_location": {"type": "string", "title": "Return Location"}, "damaged_asset_ids": {"items": {"type": "integer"}, "type": "array", "title": "Damaged Asset Ids", "default": []}, "update_master_quantities": {"type": "boolean", "title": "Update Master Quantities", "default": false}, "quantity_updates": {"items": {"$ref": "#/components/schemas/QuantityUpdate"}, "type": "array", "title": "Quantity Updates", "default": []}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["unpacked_by", "return_location"], "title": "UnpackRequest"}, "UserCreate": {"properties": {"first_name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "First Name"}, "last_name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Last Name"}, "email": {"type": "string", "format": "email", "title": "Email"}, "mobile": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Mobile"}, "user_group": {"type": "string", "maxLength": 100, "minLength": 1, "title": "User Group"}, "login_enabled": {"type": "boolean", "title": "Login Enabled", "default": true}, "login_id": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Login Id"}, "access_level": {"type": "string", "enum": ["county", "state", "precinct"], "title": "Access Level"}, "state": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "State"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct"}, "status": {"type": "boolean", "title": "Status", "default": true}, "image": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Image"}, "company": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Company"}, "employee_no": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Employee No"}, "manager": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Manager"}, "department": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Department"}, "location": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Location"}, "address_line1": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line1"}, "address_line2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line2"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City"}, "pincode": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Pincode"}, "country": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Country"}, "role": {"type": "string", "enum": ["admin", "manager", "user", "Portal Admin"], "title": "Role", "default": "user"}, "username": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Username"}, "password": {"type": "string", "maxLength": 255, "minLength": 6, "title": "Password"}}, "type": "object", "required": ["first_name", "last_name", "email", "mobile", "user_group", "login_id", "access_level", "password"], "title": "UserCreate"}, "UserGroupResponse": {"properties": {"id": {"type": "string", "title": "Id"}, "name": {"type": "string", "title": "Name"}, "members": {"type": "integer", "title": "Members"}, "status": {"type": "boolean", "title": "Status"}, "created_at": {"type": "string", "title": "Created At"}}, "type": "object", "required": ["id", "name", "members", "status", "created_at"], "title": "UserGroupResponse"}, "UserListResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "default": true}, "data": {"type": "object", "title": "Data"}, "message": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Message"}}, "type": "object", "required": ["data"], "title": "UserListResponse"}, "UserResponse": {"properties": {"first_name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "First Name"}, "last_name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Last Name"}, "email": {"type": "string", "format": "email", "title": "Email"}, "mobile": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Mobile"}, "user_group": {"type": "string", "maxLength": 100, "minLength": 1, "title": "User Group"}, "login_enabled": {"type": "boolean", "title": "Login Enabled", "default": true}, "login_id": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Login Id"}, "access_level": {"type": "string", "enum": ["county", "state", "precinct"], "title": "Access Level"}, "state": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "State"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct"}, "status": {"type": "boolean", "title": "Status", "default": true}, "image": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Image"}, "company": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Company"}, "employee_no": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Employee No"}, "manager": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Manager"}, "department": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Department"}, "location": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Location"}, "address_line1": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line1"}, "address_line2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line2"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City"}, "pincode": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Pincode"}, "country": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Country"}, "role": {"type": "string", "enum": ["admin", "manager", "user", "Portal Admin"], "title": "Role", "default": "user"}, "username": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Username"}, "id": {"type": "string", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "full_name": {"type": "string", "title": "Full Name"}}, "type": "object", "required": ["first_name", "last_name", "email", "mobile", "user_group", "login_id", "access_level", "id", "created_at", "updated_at", "full_name"], "title": "UserResponse"}, "UserUpdate": {"properties": {"first_name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Last Name"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "mobile": {"anyOf": [{"type": "string", "maxLength": 20, "minLength": 1}, {"type": "null"}], "title": "Mobile"}, "user_group": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "User Group"}, "login_enabled": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Login Enabled"}, "login_id": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Login Id"}, "access_level": {"anyOf": [{"type": "string", "enum": ["county", "state", "precinct"]}, {"type": "null"}], "title": "Access Level"}, "county": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "County"}, "precinct": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Precinct"}, "status": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}, "image": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Image"}, "company": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Company"}, "employee_no": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Employee No"}, "manager": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Manager"}, "department": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Department"}, "location": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Location"}, "address_line1": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line1"}, "address_line2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line2"}, "city": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "City"}, "state": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "State"}, "pincode": {"anyOf": [{"type": "string", "maxLength": 20}, {"type": "null"}], "title": "Pincode"}, "country": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Country"}, "role": {"anyOf": [{"type": "string", "enum": ["admin", "manager", "user", "Portal Admin"]}, {"type": "null"}], "title": "Role"}, "username": {"anyOf": [{"type": "string", "maxLength": 100}, {"type": "null"}], "title": "Username"}}, "type": "object", "title": "UserUpdate"}, "ValidTransitionsResponse": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id"}, "current_status": {"type": "string", "title": "Current Status"}, "valid_transitions": {"items": {"$ref": "#/components/schemas/WorkflowStateTransitionResponse"}, "type": "array", "title": "Valid Transitions"}}, "type": "object", "required": ["asset_id", "current_status", "valid_transitions"], "title": "ValidTransitionsResponse"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}, "VendorAddressBase": {"properties": {"type": {"type": "string", "maxLength": 50, "minLength": 1, "title": "Type"}, "address_line_1": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Address Line 1"}, "address_line_2": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Address Line 2"}, "city": {"type": "string", "maxLength": 100, "minLength": 1, "title": "City"}, "state": {"type": "string", "maxLength": 50, "minLength": 1, "title": "State"}, "zip_code": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Zip Code"}, "country": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Country", "default": "USA"}, "isPrimary": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Isprimary", "default": false}}, "type": "object", "required": ["type", "address_line_1", "city", "state", "zip_code"], "title": "VendorAddressBase"}, "VendorContactBase": {"properties": {"name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Name"}, "title": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Title"}, "phone": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Phone"}, "email": {"type": "string", "format": "email", "title": "Email"}, "isPrimary": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Isprimary", "default": false}}, "type": "object", "required": ["name", "phone", "email"], "title": "VendorContactBase"}, "VendorCreate": {"properties": {"company_name": {"type": "string", "maxLength": 255, "minLength": 1, "title": "Company Name"}, "title": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Title"}, "first_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "First Name"}, "last_name": {"type": "string", "maxLength": 100, "minLength": 1, "title": "Last Name"}, "phone": {"type": "string", "maxLength": 20, "minLength": 1, "title": "Phone"}, "email": {"type": "string", "format": "email", "title": "Email"}, "status": {"type": "boolean", "title": "Status", "default": true}, "secondary_contacts": {"anyOf": [{"items": {"$ref": "#/components/schemas/VendorContactBase"}, "type": "array"}, {"type": "null"}], "title": "Secondary Contacts", "default": []}, "addresses": {"anyOf": [{"items": {"$ref": "#/components/schemas/VendorAddressBase"}, "type": "array"}, {"type": "null"}], "title": "Addresses", "default": []}}, "type": "object", "required": ["company_name", "first_name", "last_name", "phone", "email"], "title": "VendorCreate"}, "VendorUpdate": {"properties": {"company_name": {"anyOf": [{"type": "string", "maxLength": 255, "minLength": 1}, {"type": "null"}], "title": "Company Name"}, "title": {"anyOf": [{"type": "string", "maxLength": 10}, {"type": "null"}], "title": "Title"}, "first_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "First Name"}, "last_name": {"anyOf": [{"type": "string", "maxLength": 100, "minLength": 1}, {"type": "null"}], "title": "Last Name"}, "phone": {"anyOf": [{"type": "string", "maxLength": 20, "minLength": 1}, {"type": "null"}], "title": "Phone"}, "email": {"anyOf": [{"type": "string", "format": "email"}, {"type": "null"}], "title": "Email"}, "status": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Status"}, "secondary_contacts": {"anyOf": [{"items": {"$ref": "#/components/schemas/VendorContactBase"}, "type": "array"}, {"type": "null"}], "title": "Secondary Contacts"}, "addresses": {"anyOf": [{"items": {"$ref": "#/components/schemas/VendorAddressBase"}, "type": "array"}, {"type": "null"}], "title": "Addresses"}}, "type": "object", "title": "VendorUpdate"}, "WorkflowCheckoutRequest": {"properties": {"workflow_type": {"type": "string", "pattern": "^(rolling_cage|packing_list|distribution)$", "title": "Workflow Type"}, "workflow_id": {"type": "string", "title": "Workflow Id"}, "election_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Election Id"}, "from_location_id": {"type": "string", "title": "From Location Id"}, "items": {"items": {"$ref": "#/components/schemas/CheckoutItemBase"}, "type": "array", "title": "Items"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}}, "type": "object", "required": ["workflow_type", "workflow_id", "from_location_id", "items"], "title": "WorkflowCheckoutRequest"}, "WorkflowModule": {"type": "string", "enum": ["LA_CHECKLIST", "SUPPLY_CHECKLIST", "PACKING", "CHECKOUT", "TRANSFER", "MAINTENANCE", "DAMAGE_REPORT", "MANUAL"], "title": "WorkflowModule"}, "WorkflowModuleEnum": {"type": "string", "enum": ["LA_CHECKLIST", "SUPPLY_CHECKLIST", "PACKING", "CHECKOUT", "TRANSFER", "MAINTENANCE", "DAMAGE_REPORT", "MANUAL"], "title": "WorkflowModuleEnum"}, "WorkflowStateTransitionResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "from_status": {"type": "string", "title": "From Status"}, "to_status": {"type": "string", "title": "To Status"}, "workflow_module": {"type": "string", "title": "Workflow Module"}, "transition_name": {"type": "string", "title": "Transition Name"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description"}, "business_rule": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Business Rule"}, "is_active": {"type": "boolean", "title": "Is Active"}, "requires_approval": {"type": "boolean", "title": "Requires Approval"}, "approval_role": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Approval Role"}, "priority": {"type": "integer", "title": "Priority"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["id", "from_status", "to_status", "workflow_module", "transition_name", "description", "business_rule", "is_active", "requires_approval", "approval_role", "priority", "created_at", "updated_at"], "title": "WorkflowStateTransitionResponse"}, "WorkflowType": {"type": "string", "enum": ["packing_list", "rolling_cage"], "title": "WorkflowType"}, "WorkflowValidationRequest": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id", "description": "ID of the asset"}, "to_status": {"type": "string", "title": "To Status", "description": "Target status"}, "workflow_module": {"allOf": [{"$ref": "#/components/schemas/WorkflowModuleEnum"}], "description": "Workflow module"}, "context": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Context", "description": "Additional context data"}}, "type": "object", "required": ["asset_id", "to_status", "workflow_module"], "title": "WorkflowValidationRequest"}, "WorkflowValidationResponse": {"properties": {"can_transition": {"type": "boolean", "title": "Can Transition"}, "message": {"type": "string", "title": "Message"}, "asset_id": {"type": "integer", "title": "Asset Id"}, "current_status": {"type": "string", "title": "Current Status"}, "target_status": {"type": "string", "title": "Target Status"}, "workflow_module": {"type": "string", "title": "Workflow Module"}}, "type": "object", "required": ["can_transition", "message", "asset_id", "current_status", "target_status", "workflow_module"], "title": "WorkflowValidationResponse"}, "app__schemas__asset_status_history__AssetStatusHistoryResponse": {"properties": {"asset_id": {"type": "integer", "title": "Asset Id"}, "previous_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Previous Status"}, "new_status": {"type": "string", "title": "New Status"}, "change_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Change Reason"}, "workflow_module": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Module"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "id": {"type": "integer", "title": "Id"}, "changed_by": {"type": "integer", "title": "Changed By"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["asset_id", "new_status", "id", "changed_by", "created_at"], "title": "AssetStatusHistoryResponse"}, "app__schemas__workflow__AssetStatusHistoryResponse": {"properties": {"id": {"type": "integer", "title": "Id"}, "asset_id": {"type": "integer", "title": "Asset Id"}, "previous_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Previous Status"}, "new_status": {"type": "string", "title": "New Status"}, "changed_by": {"type": "integer", "title": "Changed By"}, "change_reason": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Change Reason"}, "workflow_module": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Workflow Module"}, "session_id": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Session Id"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}}, "type": "object", "required": ["id", "asset_id", "previous_status", "new_status", "changed_by", "change_reason", "workflow_module", "session_id", "notes", "created_at"], "title": "AssetStatusHistoryResponse"}}, "securitySchemes": {"HTTPBearer": {"type": "http", "scheme": "bearer"}}}}