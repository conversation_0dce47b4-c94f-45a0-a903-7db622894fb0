#!/usr/bin/env python3
"""
Check what data actually exists in the database
"""
import sqlite3
import sys
from pathlib import Path

def check_actual_data():
    """Check what data exists in the assets table"""
    db_path = "asset_management.db"
    
    if not Path(db_path).exists():
        print(f"❌ Database file {db_path} not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if assets table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='assets'")
        if not cursor.fetchone():
            print("❌ Assets table does not exist!")
            return False
        
        print("✅ Assets table exists!")
        
        # Get table structure
        cursor.execute("PRAGMA table_info(assets)")
        columns = cursor.fetchall()
        print("\n📋 Table structure:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # Count total records
        cursor.execute("SELECT COUNT(*) FROM assets")
        total_count = cursor.fetchone()[0]
        print(f"\n📊 Total records: {total_count}")
        
        if total_count > 0:
            # Check distinct status values
            cursor.execute("SELECT DISTINCT status FROM assets WHERE status IS NOT NULL")
            status_values = [row[0] for row in cursor.fetchall()]
            print(f"\n📊 Current status values: {status_values}")
            
            # Check distinct condition values
            cursor.execute("SELECT DISTINCT condition FROM assets WHERE condition IS NOT NULL")
            condition_values = [row[0] for row in cursor.fetchall()]
            print(f"📊 Current condition values: {condition_values}")
            
            # Show sample records
            cursor.execute("SELECT asset_id, status, condition FROM assets LIMIT 5")
            sample_records = cursor.fetchall()
            print(f"\n📝 Sample records:")
            for record in sample_records:
                print(f"  Asset: {record[0]}, Status: {record[1]}, Condition: {record[2]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")
        return False

if __name__ == "__main__":
    check_actual_data()
