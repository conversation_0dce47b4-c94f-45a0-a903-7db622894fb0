# app/services/asset_workflow_service.py
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.models.assets import Asset
from app.models.asset_status_history import AssetStatusHistory
from app.models.workflow_state_transitions import WorkflowStateTransition, WorkflowModule
from app.models.user import User
import json
import logging
from datetime import datetime

logger = logging.getLogger(__name__)

class AssetWorkflowService:
    """Service to handle asset status transitions and workflow validation."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_valid_transitions(self, current_status: str, workflow_module: WorkflowModule) -> List[WorkflowStateTransition]:
        """Get valid transitions for current status and workflow module."""
        transitions = self.db.query(WorkflowStateTransition).filter(
            and_(
                WorkflowStateTransition.is_active == True,
                WorkflowStateTransition.workflow_module == workflow_module,
                or_(
                    WorkflowStateTransition.from_status == current_status,
                    WorkflowStateTransition.from_status == "*"  # Universal transitions
                )
            )
        ).order_by(WorkflowStateTransition.priority.desc()).all()
        
        return transitions
    
    def can_transition(self, asset_id: int, to_status: str, workflow_module: WorkflowModule, 
                      user_id: int, context: Optional[Dict[str, Any]] = None) -> tuple[bool, str]:
        """Check if asset can transition to new status."""
        try:
            # Get current asset
            asset = self.db.query(Asset).filter(Asset.id == asset_id).first()
            if not asset:
                return False, "Asset not found"
            
            current_status = asset.status.value if asset.status else "Unknown"
            
            # Get valid transitions
            valid_transitions = self.get_valid_transitions(current_status, workflow_module)
            
            # Find matching transition
            matching_transition = None
            for transition in valid_transitions:
                if transition.to_status == to_status:
                    matching_transition = transition
                    break
            
            if not matching_transition:
                return False, f"No valid transition from '{current_status}' to '{to_status}' for {workflow_module.value}"
            
            # Check approval requirements
            if matching_transition.requires_approval:
                user = self.db.query(User).filter(User.id == user_id).first()
                if not user or not self._user_has_approval_role(user, matching_transition.approval_role):
                    return False, f"User does not have required approval role: {matching_transition.approval_role}"
            
            # Validate conditions if specified
            if matching_transition.condition_rules:
                condition_valid, condition_message = self._validate_conditions(
                    asset, matching_transition.condition_rules, context or {}
                )
                if not condition_valid:
                    return False, f"Condition validation failed: {condition_message}"
            
            # Validate business rules if specified
            if matching_transition.validation_rules:
                validation_valid, validation_message = self._validate_business_rules(
                    asset, matching_transition.validation_rules, context or {}
                )
                if not validation_valid:
                    return False, f"Business rule validation failed: {validation_message}"
            
            return True, "Transition is valid"
            
        except Exception as e:
            logger.error(f"Error checking transition validity: {str(e)}")
            return False, f"Error validating transition: {str(e)}"
    
    def transition_asset_status(self, asset_id: int, to_status: str, workflow_module: WorkflowModule,
                               user_id: int, change_reason: str, session_id: Optional[str] = None,
                               notes: Optional[str] = None, context: Optional[Dict[str, Any]] = None) -> tuple[bool, str]:
        """Transition asset to new status with validation and history tracking."""
        try:
            # Validate transition
            can_transition, validation_message = self.can_transition(
                asset_id, to_status, workflow_module, user_id, context
            )
            
            if not can_transition:
                return False, validation_message
            
            # Get asset
            asset = self.db.query(Asset).filter(Asset.id == asset_id).first()
            if not asset:
                return False, "Asset not found"
            
            previous_status = asset.status if asset.status else "Unknown"

            # Update asset status (no validation needed for VARCHAR)
            asset.status = to_status
            
            # Create status history record
            status_history = AssetStatusHistory(
                asset_id=asset_id,
                previous_status=previous_status,
                new_status=to_status,
                changed_by=user_id,
                change_reason=change_reason,
                workflow_module=workflow_module.value,
                session_id=session_id,
                notes=notes
            )
            
            self.db.add(status_history)
            
            # Execute auto-actions if specified
            transition = self.db.query(WorkflowStateTransition).filter(
                and_(
                    WorkflowStateTransition.from_status.in_([previous_status, "*"]),
                    WorkflowStateTransition.to_status == to_status,
                    WorkflowStateTransition.workflow_module == workflow_module,
                    WorkflowStateTransition.is_active == True
                )
            ).first()
            
            if transition and transition.auto_actions:
                self._execute_auto_actions(asset, transition.auto_actions, context or {})
            
            self.db.commit()
            
            logger.info(f"Asset {asset.asset_id} status changed from {previous_status} to {to_status} by user {user_id}")
            return True, f"Asset status successfully changed to {to_status}"
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error transitioning asset status: {str(e)}")
            return False, f"Error transitioning asset status: {str(e)}"
    
    def get_asset_status_history(self, asset_id: int, limit: int = 50) -> List[AssetStatusHistory]:
        """Get status history for an asset."""
        return self.db.query(AssetStatusHistory).filter(
            AssetStatusHistory.asset_id == asset_id
        ).order_by(AssetStatusHistory.created_at.desc()).limit(limit).all()
    
    def get_assets_by_status_for_workflow(self, status: str, workflow_module: WorkflowModule) -> List[Asset]:
        """Get assets with specific status that can be processed by workflow module."""
        return self.db.query(Asset).filter(Asset.status == status).all()
    
    def _user_has_approval_role(self, user: User, required_role: str) -> bool:
        """Check if user has required approval role."""
        # This should be implemented based on your user role system
        # For now, return True for admin users
        return user.role == "admin" if hasattr(user, 'role') else True
    
    def _validate_conditions(self, asset: Asset, condition_rules: str, context: Dict[str, Any]) -> tuple[bool, str]:
        """Validate transition conditions."""
        try:
            conditions = json.loads(condition_rules)
            
            # Example condition validation
            for condition in conditions:
                condition_type = condition.get("type")
                
                if condition_type == "asset_condition":
                    required_condition = condition.get("value")
                    if asset.condition.value != required_condition:
                        return False, f"Asset condition must be {required_condition}"
                
                elif condition_type == "context_value":
                    key = condition.get("key")
                    required_value = condition.get("value")
                    if context.get(key) != required_value:
                        return False, f"Context value {key} must be {required_value}"
            
            return True, "All conditions satisfied"
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error validating conditions: {str(e)}")
            return False, f"Invalid condition rules: {str(e)}"
    
    def _validate_business_rules(self, asset: Asset, validation_rules: str, context: Dict[str, Any]) -> tuple[bool, str]:
        """Validate business rules."""
        try:
            rules = json.loads(validation_rules)
            
            # Example business rule validation
            for rule in rules:
                rule_type = rule.get("type")
                
                if rule_type == "la_checklist_complete":
                    # Check if all L&A checklist items are complete
                    session_id = context.get("session_id")
                    if not session_id:
                        return False, "L&A checklist session ID required"
                
                elif rule_type == "packing_verified":
                    # Check if packing is verified
                    if not context.get("packing_verified"):
                        return False, "Packing must be verified before status change"
            
            return True, "All business rules satisfied"
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error validating business rules: {str(e)}")
            return False, f"Invalid business rules: {str(e)}"
    
    def _execute_auto_actions(self, asset: Asset, auto_actions: str, context: Dict[str, Any]):
        """Execute automatic actions after status transition."""
        try:
            actions = json.loads(auto_actions)
            
            for action in actions:
                action_type = action.get("type")
                
                if action_type == "update_location":
                    new_location = action.get("value", context.get("new_location"))
                    if new_location:
                        asset.location = new_location
                
                elif action_type == "assign_to":
                    assignee = action.get("value", context.get("assigned_to"))
                    if assignee:
                        asset.assigned_to = assignee
                
                elif action_type == "update_last_checked":
                    asset.last_checked = datetime.utcnow()
                
                # Add more auto-actions as needed
            
        except (json.JSONDecodeError, KeyError) as e:
            logger.error(f"Error executing auto-actions: {str(e)}")


# Workflow module constants for easy import
class WorkflowModules:
    LA_CHECKLIST = WorkflowModule.LA_CHECKLIST
    SUPPLY_CHECKLIST = WorkflowModule.SUPPLY_CHECKLIST
    PACKING = WorkflowModule.PACKING
    CHECKOUT = WorkflowModule.CHECKOUT
    TRANSFER = WorkflowModule.TRANSFER
    MAINTENANCE = WorkflowModule.MAINTENANCE
    DAMAGE_REPORT = WorkflowModule.DAMAGE_REPORT
    MANUAL = WorkflowModule.MANUAL
