#!/usr/bin/env python3
"""
Create SQLite database with simple schema - no enum restrictions
"""
import sys
import os
import sqlite3
from pathlib import Path

def create_simple_assets_table():
    """Create a simple assets table without enum restrictions"""
    db_path = "asset_management.db"
    
    try:
        # Remove existing database if it exists
        if Path(db_path).exists():
            os.remove(db_path)
            print(f"🗑️ Removed existing database: {db_path}")
        
        # Create new database
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"🔄 Creating new SQLite database: {db_path}")
        
        # Create assets table with simple VARCHAR fields (no enum restrictions)
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS assets (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                asset_id VARCHAR(50) UNIQUE,
                type VARCHAR(100),
                category VARCHAR(100),
                model VARCHAR(200),
                serial_number VARCHAR(200),
                status VARCHAR(50),
                condition VARCHAR(50),
                location VARCHAR(200),
                assigned_to VARCHAR(200),
                county VARCHAR(100),
                precinct VARCHAR(100),
                purchase_date DATE,
                warranty_expiry DATE,
                last_maintenance DATE,
                next_maintenance DATE,
                last_checked DATE,
                notes TEXT,
                specifications TEXT,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("✅ Assets table created successfully!")
        
        # Create vendors table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS vendors (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                company_name VARCHAR(200) NOT NULL,
                title VARCHAR(50),
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                phone VARCHAR(50),
                email VARCHAR(200),
                status BOOLEAN DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("✅ Vendors table created successfully!")
        
        # Create users table for authentication
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username VARCHAR(100) UNIQUE NOT NULL,
                email VARCHAR(200) UNIQUE NOT NULL,
                password_hash VARCHAR(255) NOT NULL,
                first_name VARCHAR(100),
                last_name VARCHAR(100),
                is_active BOOLEAN DEFAULT 1,
                is_admin BOOLEAN DEFAULT 0,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        print("✅ Users table created successfully!")
        
        # Insert some sample data to test
        print("\\n📝 Inserting sample data...")
        
        # Sample assets
        sample_assets = [
            ("ASS0001", "Electronic Poll Book", "Hardware", "Model X1", "EPB001", "Ready", "Good", "County Office", None, "Greenville", "Precinct 1"),
            ("ASS0002", "Ballot Scanner", "Hardware", "Scanner Pro", "BS002", "New", "Excellent", "Storage", None, "Charleston", "Precinct 2"),
            ("ASS0003", "Voting Machine", "Hardware", "VoteMaster", "VM003", "Under Maintenance", "Fair", "Repair Shop", "Tech Team", "York", "Precinct 3")
        ]
        
        for asset in sample_assets:
            cursor.execute("""
                INSERT INTO assets (asset_id, type, category, model, serial_number, status, condition, location, assigned_to, county, precinct)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, asset)
        
        # Sample vendors
        sample_vendors = [
            ("TechCorp Solutions", "Mr.", "John", "Smith", "555-0123", "<EMAIL>", 1),
            ("VoteTech Industries", "Ms.", "Sarah", "Johnson", "555-0456", "<EMAIL>", 1),
            ("Election Services Inc", "Dr.", "Mike", "Davis", "555-0789", "<EMAIL>", 1)
        ]
        
        for vendor in sample_vendors:
            cursor.execute("""
                INSERT INTO vendors (company_name, title, first_name, last_name, phone, email, status)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, vendor)
        
        # Sample user (for testing)
        cursor.execute("""
            INSERT INTO users (username, email, password_hash, first_name, last_name, is_admin)
            VALUES ('admin', '<EMAIL>', 'hashed_password_here', 'Admin', 'User', 1)
        """)
        
        conn.commit()
        
        # Verify the data
        cursor.execute("SELECT COUNT(*) FROM assets")
        asset_count = cursor.fetchone()[0]
        print(f"📊 Assets created: {asset_count}")
        
        cursor.execute("SELECT COUNT(*) FROM vendors") 
        vendor_count = cursor.fetchone()[0]
        print(f"📊 Vendors created: {vendor_count}")
        
        cursor.execute("SELECT asset_id, status, condition FROM assets")
        print("\\n📝 Sample assets:")
        for row in cursor.fetchall():
            print(f"  {row[0]} - Status: {row[1]} - Condition: {row[2]}")
        
        conn.close()
        print(f"\\n✅ Database created successfully at: {os.path.abspath(db_path)}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        return False

if __name__ == "__main__":
    success = create_simple_assets_table()
    if success:
        print("\\n🎉 Ready to test! You can now:")
        print("  1. Start the backend server")
        print("  2. Test the /api/assets endpoint")
        print("  3. Add new assets through the frontend")
        sys.exit(0)
    else:
        sys.exit(1)
