#!/usr/bin/env python3
"""
Fix database enum values to match Python model expectations.
The database has values like 'New', 'Good' but Python model expects 'NEW', 'GOOD'.
"""
import sqlite3
import sys
from pathlib import Path

def fix_database_enum_values():
    """Fix enum values in the database to match Python model"""
    db_path = "asset_management.db"
    
    if not Path(db_path).exists():
        print(f"❌ Database file {db_path} not found!")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("🔍 Checking current enum values in database...")
        
        # Check current status values
        cursor.execute("SELECT DISTINCT status FROM assets WHERE status IS NOT NULL")
        status_values = [row[0] for row in cursor.fetchall()]
        print(f"📊 Current status values: {status_values}")
        
        # Check current condition values
        cursor.execute("SELECT DISTINCT condition FROM assets WHERE condition IS NOT NULL")
        condition_values = [row[0] for row in cursor.fetchall()]
        print(f"📊 Current condition values: {condition_values}")
        
        # Define the mapping from database values to Python enum values
        status_mapping = {
            'New': 'NEW',
            'Ready': 'READY', 
            'In Transfer': 'IN_TRANSFER',
            'Using': 'USING',
            'Under Maintenance': 'UNDER_MAINTENANCE',
            'Damaged': 'DAMAGED',
            'Retired': 'RETIRED',
            'Failed': 'FAILED'
        }
        
        condition_mapping = {
            'Excellent': 'EXCELLENT',
            'Good': 'GOOD',
            'Fair': 'FAIR', 
            'Poor': 'POOR',
            'Damaged': 'DAMAGED'
        }
        
        # Update status values
        print("\\n🔄 Updating status values...")
        for old_value, new_value in status_mapping.items():
            cursor.execute("UPDATE assets SET status = ? WHERE status = ?", (new_value, old_value))
            affected = cursor.rowcount
            if affected > 0:
                print(f"   ✅ Updated {affected} records: '{old_value}' → '{new_value}'")
        
        # Update condition values  
        print("\\n🔄 Updating condition values...")
        for old_value, new_value in condition_mapping.items():
            cursor.execute("UPDATE assets SET condition = ? WHERE condition = ?", (new_value, old_value))
            affected = cursor.rowcount
            if affected > 0:
                print(f"   ✅ Updated {affected} records: '{old_value}' → '{new_value}'")
        
        # Commit the changes
        conn.commit()
        
        # Verify the changes
        print("\\n🔍 Verifying updated values...")
        cursor.execute("SELECT DISTINCT status FROM assets WHERE status IS NOT NULL")
        new_status_values = [row[0] for row in cursor.fetchall()]
        print(f"📊 Updated status values: {new_status_values}")
        
        cursor.execute("SELECT DISTINCT condition FROM assets WHERE condition IS NOT NULL")
        new_condition_values = [row[0] for row in cursor.fetchall()]
        print(f"📊 Updated condition values: {new_condition_values}")
        
        print("\\n✅ Database enum values updated successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Error updating database enum values: {e}")
        return False
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    success = fix_database_enum_values()
    sys.exit(0 if success else 1)
