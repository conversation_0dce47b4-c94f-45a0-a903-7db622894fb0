// Frontend Vendor Service
import { api<PERSON><PERSON>, ApiError, testApiConnection } from '@/utils/api';

export interface Vendor {
  id: number;
  companyName: string;
  title?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  status: boolean;
  secondaryContacts?: VendorSecondaryContact[];
  addresses?: VendorAddress[];
  createdAt?: string;
  updatedAt?: string;
}

export interface VendorSecondaryContact {
  id: number;
  relation: string;
  firstName: string;
  lastName: string;
  workPhone: string;
  homePhone: string;
  cellPhone?: string;
  fax?: string;
  email: string;
  status: boolean;
}

export interface VendorAddress {
  id: number;
  type: string;
  reference: string;
  addressLine1: string;
  addressLine2?: string;
  city: string;
  state?: string;
  zip: string;
  status: boolean;
}

export interface VendorCreationData {
  companyName: string;
  title?: string;
  firstName: string;
  lastName: string;
  phone: string;
  email: string;
  status: boolean;
  secondaryContacts?: VendorSecondaryContact[];
  addresses?: VendorAddress[];
}

class VendorService {  // Get all vendors
  async getAllVendors(params?: {
    search?: string;
    status?: boolean;
    page?: number;
    limit?: number;
  }): Promise<{ vendors: Vendor[]; pagination: any; count: number }> {
    try {
      const queryParams = new URLSearchParams();
      
      if (params?.search) queryParams.append('search', params.search);
      if (params?.status !== undefined) queryParams.append('status', params.status.toString());
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      
      const endpoint = `/api/vendors${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiCall<{vendors: Vendor[]; pagination: any}>(endpoint);
      
      if (response.success && response.data) {
        return {
          vendors: response.data.vendors || [],
          pagination: response.data.pagination || { total: 0, page: 1, limit: 50, totalPages: 0 },
          count: response.data.pagination?.total || 0
        };
      }
      throw new Error(response.message || 'Failed to fetch vendors');
    } catch (error) {
      console.error('Error fetching vendors:', error);
      throw error;
    }
  }
  // Get vendor by ID
  async getVendorById(id: number): Promise<Vendor> {
    try {
      const response = await apiCall<Vendor>(`/api/vendors/${id}`);
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to fetch vendor');
    } catch (error) {
      console.error('Error fetching vendor by ID:', error);
      throw error;
    }
  }

  // Create new vendor
  async createVendor(vendorData: VendorCreationData): Promise<Vendor> {
    try {
      const response = await apiCall<Vendor>('/api/vendors', {
        method: 'POST',
        body: JSON.stringify(vendorData),
      });
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to create vendor');
    } catch (error) {
      console.error('Error creating vendor:', error);
      throw error;
    }
  }

  // Update vendor
  async updateVendor(id: number, vendorData: Partial<VendorCreationData>): Promise<Vendor> {
    try {
      const response = await apiCall<Vendor>(`/api/vendors/${id}`, {
        method: 'PUT',
        body: JSON.stringify(vendorData),
      });
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to update vendor');
    } catch (error) {
      console.error('Error updating vendor:', error);
      throw error;
    }
  }

  // Delete vendor
  async deleteVendor(id: number): Promise<boolean> {
    try {
      const response = await apiCall<boolean>(`/api/vendors/${id}`, {
        method: 'DELETE',
      });
      return response.success;
    } catch (error) {
      console.error('Error deleting vendor:', error);
      throw error;
    }
  }
  // Update secondary contacts
  async updateSecondaryContacts(id: number, secondaryContacts: VendorSecondaryContact[]): Promise<VendorSecondaryContact[]> {
    try {
      const response = await apiCall<VendorSecondaryContact[]>(`/api/vendors/${id}/secondary-contacts`, {
        method: 'PUT',
        body: JSON.stringify({ secondaryContacts }),
      });
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to update secondary contacts');
    } catch (error) {
      console.error('Error updating secondary contacts:', error);
      throw error;
    }
  }

  // Update addresses
  async updateAddresses(id: number, addresses: VendorAddress[]): Promise<VendorAddress[]> {
    try {
      const response = await apiCall<VendorAddress[]>(`/api/vendors/${id}/addresses`, {
        method: 'PUT',
        body: JSON.stringify({ addresses }),
      });
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to update addresses');
    } catch (error) {
      console.error('Error updating addresses:', error);
      throw error;
    }
  }

  // Add secondary contact
  async addSecondaryContact(vendorId: number, contact: Omit<VendorSecondaryContact, 'id'>): Promise<VendorSecondaryContact> {
    try {
      const response = await apiCall<VendorSecondaryContact>(`/api/vendors/${vendorId}/secondary-contacts`, {
        method: 'POST',
        body: JSON.stringify({ contact }),
      });
      
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to add secondary contact');
    } catch (error) {
      console.error('Error adding secondary contact:', error);
      throw error;
    }
  }

  // Add address
  async addAddress(vendorId: number, address: Omit<VendorAddress, 'id'>): Promise<VendorAddress> {
    try {
      const response = await apiCall<VendorAddress>(`/api/vendors/${vendorId}/addresses`, {
        method: 'POST',
        body: JSON.stringify({ address }),
      });
      
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to add address');
    } catch (error) {
      console.error('Error adding address:', error);
      throw error;
    }
  }

  // Get secondary contacts
  async getSecondaryContacts(vendorId: number): Promise<VendorSecondaryContact[]> {
    try {
      const response = await apiCall<VendorSecondaryContact[]>(`/api/vendors/${vendorId}/secondary-contacts`);
      
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to fetch secondary contacts');
    } catch (error) {
      console.error('Error fetching secondary contacts:', error);
      throw error;
    }
  }

  // Get addresses
  async getAddresses(vendorId: number): Promise<VendorAddress[]> {
    try {
      const response = await apiCall<VendorAddress[]>(`/api/vendors/${vendorId}/addresses`);
      
      if (response.success && response.data) {
        return response.data;
      }
      throw new Error(response.message || 'Failed to fetch addresses');
    } catch (error) {
      console.error('Error fetching addresses:', error);
      throw error;
    }
  }

  // Helper method to prepare vendors for export
  prepareVendorsForExport(vendors: Vendor[]) {
    return vendors.map(vendor => ({
      ID: vendor.id,
      'Company Name': vendor.companyName,
      'Title': vendor.title || '',
      'First Name': vendor.firstName,
      'Last Name': vendor.lastName,
      'Full Name': `${vendor.title || ''} ${vendor.firstName} ${vendor.lastName}`.trim(),
      'Phone': vendor.phone,
      'Email': vendor.email,
      'Status': vendor.status ? 'Active' : 'Inactive',
      'Secondary Contacts': vendor.secondaryContacts?.length || 0,
      'Addresses': vendor.addresses?.length || 0,
      'Created At': vendor.createdAt ? new Date(vendor.createdAt).toLocaleDateString() : '',
      'Updated At': vendor.updatedAt ? new Date(vendor.updatedAt).toLocaleDateString() : ''
    }));
  }

  // Helper method to validate vendor data
  validateVendorData(vendor: Partial<VendorCreationData>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!vendor.companyName?.trim()) {
      errors.push('Company name is required');
    }

    if (!vendor.firstName?.trim()) {
      errors.push('First name is required');
    }

    if (!vendor.lastName?.trim()) {
      errors.push('Last name is required');
    }

    if (!vendor.phone?.trim()) {
      errors.push('Phone number is required');
    }

    if (!vendor.email?.trim()) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(vendor.email)) {
      errors.push('Please enter a valid email address');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Helper method to validate secondary contact data
  validateSecondaryContactData(contact: Partial<VendorSecondaryContact>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!contact.relation?.trim()) {
      errors.push('Relation is required');
    }

    if (!contact.firstName?.trim()) {
      errors.push('First name is required');
    }

    if (!contact.lastName?.trim()) {
      errors.push('Last name is required');
    }

    if (!contact.workPhone?.trim()) {
      errors.push('Work phone is required');
    }

    if (!contact.homePhone?.trim()) {
      errors.push('Home phone is required');
    }

    if (!contact.email?.trim()) {
      errors.push('Email is required');
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(contact.email)) {
      errors.push('Please enter a valid email address');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Helper method to validate address data
  validateAddressData(address: Partial<VendorAddress>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!address.type?.trim()) {
      errors.push('Address type is required');
    }

    if (!address.reference?.trim()) {
      errors.push('Reference is required');
    }

    if (!address.addressLine1?.trim()) {
      errors.push('Address line 1 is required');
    }

    if (!address.city?.trim()) {
      errors.push('City is required');
    }

    if (!address.zip?.trim()) {
      errors.push('ZIP code is required');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
  // Test API connection
  async testConnection(): Promise<boolean> {
    return await testApiConnection();
  }

  // Bulk operations
  async bulkUpdateVendors(updates: Array<{ id: number; data: Partial<VendorCreationData> }>): Promise<Vendor[]> {
    const results: Vendor[] = [];
    const errors: string[] = [];

    for (const update of updates) {
      try {
        const result = await this.updateVendor(update.id, update.data);
        results.push(result);
      } catch (error) {
        errors.push(`Failed to update vendor ${update.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    if (errors.length > 0) {
      console.warn('Bulk update completed with errors:', errors);
    }

    return results;
  }

  // Search vendors with advanced filters
  async searchVendors(filters: {
    query?: string;
    status?: boolean;
    companyName?: string;
    email?: string;
    phone?: string;
    hasSecondaryContacts?: boolean;
    hasAddresses?: boolean;
  }): Promise<Vendor[]> {
    try {
      const queryParams = new URLSearchParams();
      
      if (filters.query) queryParams.append('search', filters.query);
      if (filters.status !== undefined) queryParams.append('status', filters.status.toString());
      
      const response = await this.getAllVendors({
        search: filters.query,
        status: filters.status,
        limit: 1000 // Get all for client-side filtering
      });

      let vendors = response.vendors;

      // Apply additional client-side filters
      if (filters.companyName) {
        vendors = vendors.filter(v => 
          v.companyName.toLowerCase().includes(filters.companyName!.toLowerCase())
        );
      }

      if (filters.email) {
        vendors = vendors.filter(v => 
          v.email.toLowerCase().includes(filters.email!.toLowerCase())
        );
      }

      if (filters.phone) {
        vendors = vendors.filter(v => 
          v.phone.includes(filters.phone!)
        );
      }

      if (filters.hasSecondaryContacts !== undefined) {
        vendors = vendors.filter(v => 
          filters.hasSecondaryContacts 
            ? (v.secondaryContacts && v.secondaryContacts.length > 0)
            : (!v.secondaryContacts || v.secondaryContacts.length === 0)
        );
      }

      if (filters.hasAddresses !== undefined) {
        vendors = vendors.filter(v => 
          filters.hasAddresses 
            ? (v.addresses && v.addresses.length > 0)
            : (!v.addresses || v.addresses.length === 0)
        );
      }

      return vendors;
    } catch (error) {
      console.error('Error searching vendors:', error);
      throw error;
    }
  }

  // Get vendor statistics
  getVendorStats(vendors: Vendor[]) {
    const total = vendors.length;
    const active = vendors.filter(v => v.status).length;
    const inactive = total - active;
    const withSecondaryContacts = vendors.filter(v => v.secondaryContacts && v.secondaryContacts.length > 0).length;
    const withAddresses = vendors.filter(v => v.addresses && v.addresses.length > 0).length;

    return {
      total,
      active,
      inactive,
      withSecondaryContacts,
      withAddresses,
      activePercentage: total > 0 ? Math.round((active / total) * 100) : 0,
      contactsPercentage: total > 0 ? Math.round((withSecondaryContacts / total) * 100) : 0,
      addressesPercentage: total > 0 ? Math.round((withAddresses / total) * 100) : 0
    };
  }

  // Import vendors from CSV data
  async importVendors(csvData: any[]): Promise<{ success: Vendor[]; errors: string[] }> {
    const results: Vendor[] = [];
    const errors: string[] = [];

    for (let i = 0; i < csvData.length; i++) {
      const row = csvData[i];
      try {
        // Map CSV fields to vendor object
        const vendor: VendorCreationData = {
          companyName: row['Company Name'] || row.companyName || '',
          title: row['Title'] || row.title || '',
          firstName: row['First Name'] || row.firstName || '',
          lastName: row['Last Name'] || row.lastName || '',
          phone: row['Phone'] || row.phone || '',
          email: row['Email'] || row.email || '',
          status: row['Status'] ? (row['Status'].toLowerCase() === 'active') : true
        };

        // Validate vendor data
        const validation = this.validateVendorData(vendor);
        if (!validation.isValid) {
          errors.push(`Row ${i + 1}: ${validation.errors.join(', ')}`);
          continue;
        }

        // Create vendor
        const result = await this.createVendor(vendor);
        results.push(result);
      } catch (error) {
        errors.push(`Row ${i + 1}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }

    return { success: results, errors };
  }

  // Export vendors to different formats
  async exportVendors(vendors: Vendor[], format: 'csv' | 'json' | 'excel'): Promise<string | Blob> {
    const exportData = this.prepareVendorsForExport(vendors);

    switch (format) {
      case 'csv': {
        const headers = Object.keys(exportData[0] || {});
        const csvContent = [
          headers.join(','),
          ...exportData.map(row => 
            headers.map(header => `"${row[header as keyof typeof row] || ''}"`).join(',')
          )
        ].join('\n');
        return csvContent;
      }

      case 'json': {
        return JSON.stringify(exportData, null, 2);
      }

      case 'excel': {
        // For Excel export, return CSV format that can be opened in Excel
        const headers = Object.keys(exportData[0] || {});
        const csvContent = [
          headers.join('\t'),
          ...exportData.map(row => 
            headers.map(header => `${row[header as keyof typeof row] || ''}`).join('\t')
          )
        ].join('\n');
        
        const blob = new Blob([csvContent], { type: 'application/vnd.ms-excel' });
        return blob;
      }

      default:
        throw new Error('Unsupported export format');
    }
  }

  // Backup and restore functionality
  async backupVendors(): Promise<string> {
    try {
      const response = await this.getAllVendors({ limit: 10000 });
      const backup = {
        timestamp: new Date().toISOString(),
        version: '1.0',
        data: response.vendors
      };
      return JSON.stringify(backup, null, 2);
    } catch (error) {
      console.error('Error creating backup:', error);
      throw error;
    }
  }

  async restoreVendors(backupData: string): Promise<{ success: number; errors: string[] }> {
    try {
      const backup = JSON.parse(backupData);
      const vendors = backup.data || [];
      
      let successCount = 0;
      const errors: string[] = [];

      for (const vendor of vendors) {
        try {
          const { id, createdAt, updatedAt, ...vendorData } = vendor;
          await this.createVendor(vendorData);
          successCount++;
        } catch (error) {
          errors.push(`Failed to restore vendor ${vendor.companyName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      return { success: successCount, errors };
    } catch (error) {
      console.error('Error restoring backup:', error);
      throw new Error('Invalid backup data format');
    }
  }
}

const vendorService = new VendorService();
export default vendorService;