#!/usr/bin/env python3
"""
Fix enum values using the same database connection as the app
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config.database import get_db, SessionLocal
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_enum_values_via_app():
    """Fix enum values using the same database session as the app"""
    try:
        logger.info("🔍 Using app's database connection to fix enum values...")
        
        db = SessionLocal()
        
        # First, let's try to see what tables exist
        try:
            # Try raw SQL to check tables 
            result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = result.fetchall()
            logger.info(f"📋 Available tables: {[t[0] for t in tables]}")
        except:
            # If SQLite fails, try MySQL syntax
            try:
                result = db.execute(text("SHOW TABLES"))
                tables = result.fetchall()
                logger.info(f"📋 Available tables: {[t[0] for t in tables]}")
            except Exception as e:
                logger.error(f"❌ Could not list tables: {e}")
                return False
        
        # Check if assets table exists
        table_exists = False
        for table in tables:
            if 'assets' in table[0].lower():
                table_exists = True
                break
        
        if not table_exists:
            logger.error("❌ No assets table found!")
            return False
        
        logger.info("✅ Assets table found!")
        
        # Check current enum values
        try:
            result = db.execute(text("SELECT DISTINCT status FROM assets WHERE status IS NOT NULL"))
            status_values = [row[0] for row in result.fetchall()]
            logger.info(f"📊 Current status values: {status_values}")
            
            result = db.execute(text("SELECT DISTINCT `condition` FROM assets WHERE `condition` IS NOT NULL"))
            condition_values = [row[0] for row in result.fetchall()]
            logger.info(f"📊 Current condition values: {condition_values}")
            
        except Exception as e:
            # Try without backticks for SQLite
            result = db.execute(text("SELECT DISTINCT condition FROM assets WHERE condition IS NOT NULL"))
            condition_values = [row[0] for row in result.fetchall()]
            logger.info(f"📊 Current condition values: {condition_values}")
        
        # Define mappings
        status_mapping = {
            'New': 'NEW',
            'Ready': 'READY', 
            'In Transfer': 'IN_TRANSFER',
            'Using': 'USING',
            'Under Maintenance': 'UNDER_MAINTENANCE',
            'Damaged': 'DAMAGED',
            'Retired': 'RETIRED',
            'Failed': 'FAILED'
        }
        
        condition_mapping = {
            'Excellent': 'EXCELLENT',
            'Good': 'GOOD',
            'Fair': 'FAIR', 
            'Poor': 'POOR',
            'Damaged': 'DAMAGED'
        }
        
        # Update status values
        logger.info("\\n🔄 Updating status values...")
        for old_value, new_value in status_mapping.items():
            if old_value in status_values:
                result = db.execute(text("UPDATE assets SET status = :new_value WHERE status = :old_value"), 
                                   {"new_value": new_value, "old_value": old_value})
                logger.info(f"   ✅ Updated status: '{old_value}' → '{new_value}'")
        
        # Update condition values
        logger.info("\\n🔄 Updating condition values...")
        for old_value, new_value in condition_mapping.items():
            if old_value in condition_values:
                try:
                    result = db.execute(text("UPDATE assets SET `condition` = :new_value WHERE `condition` = :old_value"), 
                                       {"new_value": new_value, "old_value": old_value})
                except:
                    # Try without backticks for SQLite
                    result = db.execute(text("UPDATE assets SET condition = :new_value WHERE condition = :old_value"), 
                                       {"new_value": new_value, "old_value": old_value})
                logger.info(f"   ✅ Updated condition: '{old_value}' → '{new_value}'")
        
        # Commit changes
        db.commit()
        
        # Verify changes
        logger.info("\\n🔍 Verifying changes...")
        result = db.execute(text("SELECT DISTINCT status FROM assets WHERE status IS NOT NULL"))
        new_status_values = [row[0] for row in result.fetchall()]
        logger.info(f"📊 Updated status values: {new_status_values}")
        
        try:
            result = db.execute(text("SELECT DISTINCT `condition` FROM assets WHERE `condition` IS NOT NULL"))
            new_condition_values = [row[0] for row in result.fetchall()]
        except:
            result = db.execute(text("SELECT DISTINCT condition FROM assets WHERE condition IS NOT NULL"))
            new_condition_values = [row[0] for row in result.fetchall()]
        logger.info(f"📊 Updated condition values: {new_condition_values}")
        
        logger.info("\\n✅ Enum values updated successfully!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Error fixing enum values: {e}")
        if 'db' in locals():
            db.rollback()
        return False
    finally:
        if 'db' in locals():
            db.close()

if __name__ == "__main__":
    success = fix_enum_values_via_app()
    sys.exit(0 if success else 1)
