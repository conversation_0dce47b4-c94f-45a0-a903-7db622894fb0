#!/usr/bin/env python3
"""
Check what enum values the database actually expects
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DAT<PERSON>ASE_URL

def check_database_enums():
    """Check what enum values the database expects."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            print("🔍 Checking database enum values...")

            # Get the actual enum values from the database
            result = conn.execute(text("SHOW CREATE TABLE assets"))
            table_def = result.fetchone()[1]
            
            print("Current assets table definition:")
            print(table_def)
            print("\n" + "="*80 + "\n")
            
            # Look for enum definitions in the table definition
            if "enum(" in table_def.lower():
                lines = table_def.split('\n')
                for line in lines:
                    if 'status' in line.lower() and 'enum' in line.lower():
                        print("STATUS ENUM:")
                        print(line.strip())
                    if 'condition' in line.lower() and 'enum' in line.lower():
                        print("CONDITION ENUM:")
                        print(line.strip())
                        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_database_enums()
