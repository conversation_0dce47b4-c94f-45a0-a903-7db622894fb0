#!/usr/bin/env python3
"""
Fix the category field issue in assets table - make it nullable
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_category_field():
    """Fix the category field issue in assets table."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            # First, let's see the current table structure
            logger.info("🔍 Checking current assets table structure...")

            try:
                result = conn.execute(text("DESCRIBE assets"))
                columns = result.fetchall()
                print("Current assets table columns:")
                column_names = []
                for col in columns:
                    column_names.append(col[0])
                    print(f"  {col[0]}: {col[1]} {'NULL' if col[2] == 'YES' else 'NOT NULL'} {f'DEFAULT {col[4]}' if col[4] else ''}")
                
                # Check if category column exists and make it nullable
                if 'category' in column_names:
                    logger.info("❗ Found 'category' column in database table")
                    
                    # Make category column nullable with a default value
                    logger.info("🔧 Making 'category' column nullable...")
                    conn.execute(text("ALTER TABLE assets MODIFY COLUMN category VARCHAR(255) NULL DEFAULT NULL"))
                    
                    conn.commit()
                    logger.info("✅ Fixed 'category' column successfully")
                else:
                    logger.info("ℹ️ No 'category' column found - adding it as nullable...")
                    # Add category column as nullable
                    conn.execute(text("ALTER TABLE assets ADD COLUMN category VARCHAR(255) NULL DEFAULT NULL"))
                    conn.commit()
                    logger.info("✅ Added 'category' column successfully")
                    
                # Final verification
                logger.info("📊 Final verification:")
                result = conn.execute(text("DESCRIBE assets"))
                columns = result.fetchall()
                for col in columns:
                    if col[0] == 'category':
                        print(f"  ✓ category: {col[1]} {'NULL' if col[2] == 'YES' else 'NOT NULL'} {f'DEFAULT {col[4]}' if col[4] else 'No default'}")
                        
            except Exception as e:
                logger.error(f"❌ Error checking/fixing category column: {e}")
                conn.rollback()
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_category_field()
