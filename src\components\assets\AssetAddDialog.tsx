import { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { assetService } from "@/services/assetService";
import { useToast } from "@/components/ui/use-toast";

interface AssetAddDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAssetAdded?: () => void;
}

export function AssetAddDialog({ open, onOpenChange, onAssetAdded }: AssetAddDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isLoadingAssetId, setIsLoadingAssetId] = useState(false);
  
  const [formData, setFormData] = useState({
    asset_id: "",
    serial_number: "",
    type: "",
    category: "",
    model: "",
    status: "New",
    condition: "Good",
    location: "",
    assigned_to: "",
    state: "SC",
    county: "",
    precinct: "",
    purchase_date: "",
    warranty_expiry: "",
    last_maintenance: "",
    next_maintenance: "",
    notes: ""
  });

  // Auto-generate next asset ID when dialog opens
  useEffect(() => {
    if (open && !formData.asset_id) {
      const fetchNextAssetId = async () => {
        setIsLoadingAssetId(true);
        try {
          const response = await assetService.getNextAssetId();
          if (response.success && response.asset_id) {
            setFormData(prev => ({
              ...prev,
              asset_id: response.asset_id!
            }));
          } else {
            // Fallback to default if API fails
            setFormData(prev => ({
              ...prev,
              asset_id: "ASS0001"
            }));
            toast({
              title: "Warning",
              description: response.message || "Could not auto-generate asset ID. Please set manually.",
              variant: "destructive",
            });
          }
        } catch (error) {
          console.error("Error fetching next asset ID:", error);
          // Fallback to default
          setFormData(prev => ({
            ...prev,
            asset_id: "ASS0001"
          }));
          toast({
            title: "Warning",
            description: "Could not auto-generate asset ID. Please set manually.",
            variant: "destructive",
          });
        } finally {
          setIsLoadingAssetId(false);
        }
      };

      fetchNextAssetId();
    }
  }, [open, formData.asset_id, toast]);

  // Auto-generate serial number when type changes
  useEffect(() => {
    if (formData.type) {
      // Generate a random serial number based on type
      const prefix = formData.type === "BMDs" ? "EV-" :
                    formData.type === "Poll Pads" ? "EPB-" :
                    formData.type === "Tabulators" ? "DS300-" :
                    formData.type === "AV Computers" ? "DELL-" :
                    formData.type === "Ballot Box (ICP)" ? "BB-" : "SN-";

      const randomNum = Math.floor(10000 + Math.random() * 90000); // 5-digit random number
      const serial_number = `${prefix}${randomNum}`;

      setFormData(prev => ({
        ...prev,
        serial_number
      }));
    }
  }, [formData.type]);

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type || !formData.location) {
      toast({
        title: "Validation Error",
        description: "Asset type and location are required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Prepare the data for API call with proper date handling
      const assetData = {
        asset_id: formData.asset_id,
        type: formData.type,
        category: formData.category || undefined,
        model: formData.model || undefined,
        serial_number: formData.serial_number || undefined,
        status: formData.status || "New",
        condition: formData.condition || "Good",
        location: formData.location,
        assigned_to: formData.assigned_to || undefined,
        state: formData.state || undefined,
        county: formData.county || undefined,
        precinct: formData.precinct || undefined,
        // Handle dates properly - convert to ISO format or set to null
        purchase_date: formData.purchase_date && formData.purchase_date.trim() ? 
          new Date(formData.purchase_date).toISOString() : null,
        warranty_expiry: formData.warranty_expiry && formData.warranty_expiry.trim() ? 
          new Date(formData.warranty_expiry).toISOString() : null,
        last_maintenance: formData.last_maintenance && formData.last_maintenance.trim() ? 
          new Date(formData.last_maintenance).toISOString() : null,
        next_maintenance: formData.next_maintenance && formData.next_maintenance.trim() ? 
          new Date(formData.next_maintenance).toISOString() : null,
        notes: formData.notes || undefined,
      };

      console.log("Submitting asset data:", assetData);
      
      const response = await assetService.createAssetBackend(assetData);
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Asset created successfully",
        });
        
        if (onAssetAdded) {
          onAssetAdded();
        }
        
        onOpenChange(false);
        
        // Reset form
        setFormData({
          asset_id: "",
          serial_number: "",
          type: "",
          category: "",
          model: "",
          status: "New",
          condition: "Good", // Use "Good" instead of "GOOD" to match backend enum
          location: "",
          assigned_to: "",
          state: "SC",
          county: "",
          precinct: "",
          purchase_date: "",
          warranty_expiry: "",
          last_maintenance: "",
          next_maintenance: "",
          notes: ""
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create asset",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error creating asset:", error);
      
      let errorMessage = "Failed to create asset. Please try again.";
      
      // Handle different error types
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        // Try to extract meaningful error from object
        if ((error as any).message) {
          errorMessage = (error as any).message;
        } else if ((error as any).detail) {
          errorMessage = typeof (error as any).detail === 'string' 
            ? (error as any).detail 
            : JSON.stringify((error as any).detail);
        }
      }
      
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl p-0 overflow-hidden">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Add New Asset</h2>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button size="sm" onClick={handleSubmit} disabled={isSubmitting}>
              {isSubmitting ? "Saving..." : "Save"}
            </Button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[80vh]">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Asset ID *
                </label>
                <Input
                  value={isLoadingAssetId ? "Loading..." : formData.asset_id}
                  onChange={(e) => handleInputChange("asset_id", e.target.value)}
                  readOnly={isLoadingAssetId}
                  className={isLoadingAssetId ? "bg-gray-50" : ""}
                  required
                />
                <p className="text-xs text-muted-foreground">Auto-generated from database</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Serial Number
                </label>
                <Input
                  value={formData.serial_number}
                  onChange={(e) => handleInputChange("serial_number", e.target.value)}
                  readOnly
                  className="bg-gray-50"
                />
                <p className="text-xs text-muted-foreground">Auto-generated based on type</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Asset Type *
                </label>
                <Select
                  value={formData.type}
                  onValueChange={(value) => handleInputChange("type", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Ballot Box (ICP)">Ballot Box (ICP)</SelectItem>
                    <SelectItem value="AV Computers">AV Computers</SelectItem>
                    <SelectItem value="BMDs">BMDs</SelectItem>
                    <SelectItem value="Poll Pads">Poll Pads</SelectItem>
                    <SelectItem value="Tabulators">Tabulators</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Category
                </label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleInputChange("category", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Voting Equipment">Voting Equipment</SelectItem>
                    <SelectItem value="IT Equipment">IT Equipment</SelectItem>
                    <SelectItem value="Display Equipment">Display Equipment</SelectItem>
                    <SelectItem value="Security Equipment">Security Equipment</SelectItem>
                    <SelectItem value="Storage Equipment">Storage Equipment</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Model
                </label>
                <Select
                  value={formData.model}
                  onValueChange={(value) => handleInputChange("model", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Dell Latitude 3410">Dell Latitude 3410</SelectItem>
                    <SelectItem value="Ballot Box (ICP) - 001">Ballot Box (ICP) - 001</SelectItem>
                    <SelectItem value="Acer-V206HQL">Acer-V206HQL</SelectItem>
                    <SelectItem value="BMD">BMD</SelectItem>
                    <SelectItem value="ViewSonic VA2055SM">ViewSonic VA2055SM</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Status *
                </label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="New">New</SelectItem>
                    <SelectItem value="Ready">Ready</SelectItem>
                    <SelectItem value="Failed">Failed</SelectItem>
                    <SelectItem value="Packed">Packed</SelectItem>
                    <SelectItem value="Checked-out">Checked-out</SelectItem>
                    <SelectItem value="In-transfer">In-transfer</SelectItem>
                    <SelectItem value="Delivered">Delivered</SelectItem>
                    <SelectItem value="Using">Using</SelectItem>
                    <SelectItem value="Damaged">Damaged</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Condition
                </label>
                <Select
                  value={formData.condition}
                  onValueChange={(value) => handleInputChange("condition", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Excellent">Excellent</SelectItem>
                    <SelectItem value="Good">Good</SelectItem>
                    <SelectItem value="Fair">Fair</SelectItem>
                    <SelectItem value="Poor">Poor</SelectItem>
                    <SelectItem value="Damaged">Damaged</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Location *
                </label>
                <Input
                  value={formData.location}
                  onChange={(e) => handleInputChange("location", e.target.value)}
                  placeholder="Enter location"
                  required
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Assigned To
                </label>
                <Input
                  value={formData.assigned_to}
                  onChange={(e) => handleInputChange("assigned_to", e.target.value)}
                  placeholder="Enter assignee"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  State
                </label>
                <Input
                  value={formData.state}
                  onChange={(e) => handleInputChange("state", e.target.value)}
                  placeholder="State"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  County
                </label>
                <Input
                  value={formData.county}
                  onChange={(e) => handleInputChange("county", e.target.value)}
                  placeholder="County"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Precinct
                </label>
                <Input
                  value={formData.precinct}
                  onChange={(e) => handleInputChange("precinct", e.target.value)}
                  placeholder="Precinct"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Purchase Date
                </label>
                <Input
                  type="date"
                  value={formData.purchase_date}
                  onChange={(e) => handleInputChange("purchase_date", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Warranty Expiry
                </label>
                <Input
                  type="date"
                  value={formData.warranty_expiry}
                  onChange={(e) => handleInputChange("warranty_expiry", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Last Maintenance
                </label>
                <Input
                  type="date"
                  value={formData.last_maintenance}
                  onChange={(e) => handleInputChange("last_maintenance", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Next Maintenance
                </label>
                <Input
                  type="date"
                  value={formData.next_maintenance}
                  onChange={(e) => handleInputChange("next_maintenance", e.target.value)}
                />
              </div>
              <div className="space-y-2 col-span-2">
                <label className="text-sm font-medium">
                  Notes
                </label>
                <Textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  className="h-24 resize-none"
                  placeholder="Additional notes about the asset"
                />
              </div>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
