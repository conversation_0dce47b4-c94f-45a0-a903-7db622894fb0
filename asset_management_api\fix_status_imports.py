#!/usr/bin/env python3
"""
Fix status import conflict in assets.py
"""
import re

def fix_status_imports():
    """Replace all status.HTTP_ with http_status.HTTP_ in assets.py"""
    file_path = "app/routes/assets.py"
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Replace all status.HTTP_ with http_status.HTTP_
        updated_content = re.sub(r'status\.HTTP_', 'http_status.HTTP_', content)
        
        # Write back to file
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        print(f"✅ Successfully updated all status.HTTP_ references to http_status.HTTP_ in {file_path}")
        
        # Count the replacements made
        replacements = len(re.findall(r'http_status\.HTTP_', updated_content))
        print(f"📊 Total replacements made: {replacements}")
        
    except Exception as e:
        print(f"❌ Error fixing status imports: {e}")

if __name__ == "__main__":
    fix_status_imports()
