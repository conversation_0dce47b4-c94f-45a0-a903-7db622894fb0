#!/usr/bin/env python3
"""
Check Assets Table Schema and Fix Enum Issues
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
from app.models.assets import AssetStatus, AssetCondition
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_fix_assets_schema():
    """Check the assets table schema and fix enum issues."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            # Check current table structure
            logger.info("🔍 Checking current assets table structure...")

            result = conn.execute(text("SHOW CREATE TABLE assets"))
            table_def = result.fetchone()[1]
            print("Current assets table definition:")
            print(table_def)
            print("\n" + "="*80 + "\n")
            
            # Print what the Python enums expect
            print("Python AssetStatus enum values:")
            for status in AssetStatus:
                print(f"  {status.name} = '{status.value}'")
            
            print("\nPython AssetCondition enum values:")
            for condition in AssetCondition:
                print(f"  {condition.name} = '{condition.value}'")
            
            print("\n" + "="*80 + "\n")
            
            # Try to fix the enum columns if needed
            logger.info("🔧 Attempting to fix enum columns...")
            
            # Update status column
            status_values = "','".join([status.value for status in AssetStatus])
            status_enum = f"ENUM('{status_values}')"
            
            condition_values = "','".join([condition.value for condition in AssetCondition])
            condition_enum = f"ENUM('{condition_values}')"
            
            try:
                # Modify status column
                conn.execute(text(f"ALTER TABLE assets MODIFY COLUMN status {status_enum} NOT NULL DEFAULT 'New'"))
                logger.info("✅ Updated status column successfully")
                
                # Modify condition column  
                conn.execute(text(f"ALTER TABLE assets MODIFY COLUMN `condition` {condition_enum} NOT NULL DEFAULT 'Good'"))
                logger.info("✅ Updated condition column successfully")
                
                conn.commit()
                
            except Exception as e:
                logger.error(f"❌ Error updating columns: {e}")
                conn.rollback()
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    check_and_fix_assets_schema()
