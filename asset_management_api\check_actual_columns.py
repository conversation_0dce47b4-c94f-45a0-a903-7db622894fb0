#!/usr/bin/env python3
"""
Check what columns actually exist in the assets table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL

def check_actual_columns():
    """Check the actual columns in the assets table."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            print("🔍 Checking actual assets table columns...")

            result = conn.execute(text("DESCRIBE assets"))
            columns = result.fetchall()
            print("All columns in assets table:")
            for i, col in enumerate(columns, 1):
                print(f"  {i:2d}. {col[0]:<20} | {col[1]:<15} | {'NULL' if col[2] == 'YES' else 'NOT NULL':<8} | {f'DEFAULT: {col[4]}' if col[4] else 'No default'}")
                
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_actual_columns()
