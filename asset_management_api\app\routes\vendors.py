# app/routes/vendors.py
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import or_
from app.config.database import get_db
from app.models.vendors import Vendor
from app.models.user import User
from app.middleware.auth import get_current_user, require_admin
from app.schemas.vendors import VendorCreate, VendorUpdate, VendorResponse
from typing import Optional, List
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=dict)
@router.get("", response_model=dict)  # Handle both with and without trailing slash
async def get_vendors(
    search: Optional[str] = Query(None),
    status_filter: Optional[bool] = Query(None, alias="status"),
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get all vendors with optional search and pagination.
    Equivalent to GET / in Node.js version.
    """
    try:
        query = db.query(Vendor)
        
        # Add search filter
        if search:
            search_filter = or_(
                Vendor.company_name.ilike(f"%{search}%"),
                Vendor.first_name.ilike(f"%{search}%"),
                Vendor.last_name.ilike(f"%{search}%"),
                Vendor.email.ilike(f"%{search}%"),
                Vendor.phone.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Add status filter
        if status_filter is not None:
            query = query.filter(Vendor.status == status_filter)
        
        # Get total count
        total_count = query.count()
        
        # Apply ordering first, then pagination
        offset = (page - 1) * limit
        vendors = query.order_by(Vendor.company_name.asc()).offset(offset).limit(limit).all()
        
        # Convert to dict with parsed JSON data
        vendors_with_parsed_data = []
        for vendor in vendors:
            vendor_dict = vendor.to_dict()
            vendors_with_parsed_data.append(vendor_dict)
        
        return {
            "success": True,
            "data": {
                "vendors": vendors_with_parsed_data,
                "pagination": {
                    "total": total_count,
                    "page": page,
                    "limit": limit,
                    "totalPages": (total_count + limit - 1) // limit
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching vendors: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch vendors"
        )

@router.get("/{vendor_id}", response_model=dict)
async def get_vendor(
    vendor_id: int,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get vendor by ID.
    Equivalent to GET /:id in Node.js version.
    """
    try:
        vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()
        
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        return {
            "success": True,
            "data": vendor.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching vendor: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch vendor"
        )

@router.post("/", response_model=dict)
@router.post("", response_model=dict)  # Handle both with and without trailing slash
async def create_vendor(
    vendor_data: VendorCreate,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Create new vendor.
    Equivalent to POST / in Node.js version.
    """
    try:
        # Check if email already exists
        existing_vendor = db.query(Vendor).filter(Vendor.email == vendor_data.email).first()
        if existing_vendor:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Vendor with this email already exists"
            )
        
        # Create new vendor
        new_vendor = Vendor(
            company_name=vendor_data.company_name.strip(),
            title=vendor_data.title.strip() if vendor_data.title else None,
            first_name=vendor_data.first_name.strip(),
            last_name=vendor_data.last_name.strip(),
            phone=vendor_data.phone.strip(),
            email=vendor_data.email.lower().strip(),
            status=vendor_data.status
        )
        
        # Set secondary contacts and addresses if provided
        if vendor_data.secondary_contacts:
            new_vendor.set_secondary_contacts_data(vendor_data.secondary_contacts)
        
        if vendor_data.addresses:
            new_vendor.set_addresses_data(vendor_data.addresses)
        
        # Save to database
        db.add(new_vendor)
        db.commit()
        db.refresh(new_vendor)
        
        return {
            "success": True,
            "data": new_vendor.to_dict(),
            "message": "Vendor created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating vendor: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create vendor"
        )

@router.put("/{vendor_id}", response_model=dict)
async def update_vendor(
    vendor_id: int,
    vendor_data: VendorUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update vendor.
    Equivalent to PUT /:id in Node.js version.
    """
    try:
        vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()
        
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        # Check if email already exists for another vendor
        if vendor_data.email and vendor_data.email != vendor.email:
            existing_vendor = db.query(Vendor).filter(
                Vendor.email == vendor_data.email,
                Vendor.id != vendor_id
            ).first()
            
            if existing_vendor:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail="Vendor with this email already exists"
                )
        
        # Update basic vendor fields
        update_data = vendor_data.dict(exclude_unset=True, exclude={'secondary_contacts', 'addresses'})
        
        for field, value in update_data.items():
            if hasattr(vendor, field) and value is not None:
                if isinstance(value, str):
                    setattr(vendor, field, value.strip())
                else:
                    setattr(vendor, field, value)
        
        # Update secondary contacts if provided
        if vendor_data.secondary_contacts is not None:
            vendor.set_secondary_contacts_data(vendor_data.secondary_contacts)
        
        # Update addresses if provided
        if vendor_data.addresses is not None:
            vendor.set_addresses_data(vendor_data.addresses)
        
        db.commit()
        db.refresh(vendor)
        
        return {
            "success": True,
            "data": vendor.to_dict(),
            "message": "Vendor updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating vendor: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update vendor"
        )

@router.delete("/{vendor_id}")
async def delete_vendor(
    vendor_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """
    Delete vendor.
    Equivalent to DELETE /:id in Node.js version.
    """
    try:
        vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()
        
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        db.delete(vendor)
        db.commit()
        
        return {
            "success": True,
            "message": "Vendor deleted successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting vendor: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete vendor"
        )

@router.put("/{vendor_id}/secondary-contacts", response_model=dict)
async def update_secondary_contacts(
    vendor_id: int,
    contacts_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update secondary contacts.
    Equivalent to PUT /:id/secondary-contacts in Node.js version.
    """
    try:
        vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()
        
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        secondary_contacts = contacts_data.get("secondaryContacts", [])
        vendor.set_secondary_contacts_data(secondary_contacts)
        
        db.commit()
        db.refresh(vendor)
        
        return {
            "success": True,
            "data": vendor.get_secondary_contacts_data(),
            "message": "Secondary contacts updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating secondary contacts: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update secondary contacts"
        )

@router.put("/{vendor_id}/addresses", response_model=dict)
async def update_addresses(
    vendor_id: int,
    addresses_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update addresses.
    Equivalent to PUT /:id/addresses in Node.js version.
    """
    try:
        vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()
        
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        addresses = addresses_data.get("addresses", [])
        vendor.set_addresses_data(addresses)
        
        db.commit()
        db.refresh(vendor)
        
        return {
            "success": True,
            "data": vendor.get_addresses_data(),
            "message": "Addresses updated successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating addresses: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update addresses"
        )

@router.get("/{vendor_id}/secondary-contacts", response_model=dict)
async def get_secondary_contacts(
    vendor_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get secondary contacts.
    Equivalent to GET /:id/secondary-contacts in Node.js version.
    """
    try:
        vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()
        
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        return {
            "success": True,
            "data": vendor.get_secondary_contacts_data()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching secondary contacts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch secondary contacts"
        )

@router.get("/{vendor_id}/addresses", response_model=dict)
async def get_addresses(
    vendor_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get addresses.
    Equivalent to GET /:id/addresses in Node.js version.
    """
    try:
        vendor = db.query(Vendor).filter(Vendor.id == vendor_id).first()
        
        if not vendor:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vendor not found"
            )
        
        return {
            "success": True,
            "data": vendor.get_addresses_data()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching addresses: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch addresses"
        )