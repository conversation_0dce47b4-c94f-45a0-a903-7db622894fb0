# app/routes/maintenance.py
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_
from app.config.database import get_db
from app.models.maintenance import Maintenance, MaintenanceCategory, RequestType, Priority, MaintenanceStatus, ScheduledFrequency
from app.models.assets import Asset
from app.models.user import User
from app.middleware.auth import get_current_user
from app.middleware.dev_auth import get_dev_or_real_user
from app.services.asset_workflow_service import AssetWorkflowService
from app.models.workflow_state_transitions import WorkflowModule
from typing import Optional
from datetime import datetime, timedelta
import logging

router = APIRouter()
logger = logging.getLogger(__name__)

# =============================================================================
# DEVELOPMENT ENDPOINTS (No Authentication Required)
# These endpoints bypass authentication for testing purposes
# MUST be defined BEFORE general routes to avoid conflicts
# =============================================================================

@router.get("/dev/list")
async def get_maintenance_records_dev(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    current_user: User = Depends(get_dev_or_real_user),
    db: Session = Depends(get_db)
):
    """Development endpoint - Get maintenance records without authentication."""
    try:
        offset = (page - 1) * limit
        maintenance_records = db.query(Maintenance).order_by(Maintenance.created_at.desc()).offset(offset).limit(limit).all()
        total_count = db.query(Maintenance).count()
        total_pages = (total_count + limit - 1) // limit
        
        return {
            "data": [record.to_dict() for record in maintenance_records],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": total_pages
            }
        }
    except Exception as e:
        logger.error(f"Error in dev endpoint: {e}")
        return {"data": [], "pagination": {"page": 1, "limit": limit, "total": 0, "pages": 0}}

@router.get("/dev/stats")
async def get_maintenance_stats_dev(
    current_user: User = Depends(get_dev_or_real_user),
    db: Session = Depends(get_db)
):
    """Development endpoint - Get maintenance stats without authentication."""
    try:
        total_maintenance = db.query(Maintenance).count()
        active_maintenance = db.query(Maintenance).filter(
            Maintenance.status.in_([MaintenanceStatus.REQUESTED, MaintenanceStatus.IN_PROGRESS])
        ).count()
        
        return {
            "total_maintenance": total_maintenance,
            "active_maintenance": active_maintenance,
            "completed_last_30_days": 0,
            "high_priority_tasks": 0
        }
    except Exception as e:
        logger.error(f"Error in dev stats: {e}")
        return {
            "total_maintenance": 0,
            "active_maintenance": 0,
            "completed_last_30_days": 0,
            "high_priority_tasks": 0
        }

@router.get("/dev/available-assets")
async def get_available_assets_dev(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    current_user: User = Depends(get_dev_or_real_user),
    db: Session = Depends(get_db)
):
    """Development endpoint - Get available assets without authentication."""
    try:
        offset = (page - 1) * limit
        assets = db.query(Asset).filter(
            Asset.status.in_(["Ready", "Failed"])
        ).offset(offset).limit(limit).all()
        
        return [asset.to_dict() for asset in assets]
    except Exception as e:
        logger.error(f"Error in dev available assets: {e}")
        return []

# =============================================================================
# MAIN ENDPOINTS (Authentication Required)
# =============================================================================

@router.get("/")
async def get_maintenance_records(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    search: Optional[str] = Query(None),
    category: Optional[MaintenanceCategory] = Query(None),
    status_filter: Optional[MaintenanceStatus] = Query(None, alias="status"),
    priority: Optional[Priority] = Query(None),
    request_type: Optional[RequestType] = Query(None, alias="requestType"),
    assigned_technician: Optional[str] = Query(None, alias="assignedTechnician"),
    start_date: Optional[str] = Query(None, alias="startDate"),
    end_date: Optional[str] = Query(None, alias="endDate"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all maintenance records with optional filtering.
    Equivalent to GET / in Node.js maintenance.ts
    """
    try:
        offset = (page - 1) * limit
        query = db.query(Maintenance)
        
        # Add search filters
        if search:
            search_filter = or_(
                Maintenance.asset_tag.ilike(f"%{search}%"),
                Maintenance.name.ilike(f"%{search}%"),
                Maintenance.description.ilike(f"%{search}%"),
                Maintenance.assigned_technician.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Add specific filters
        if category:
            query = query.filter(Maintenance.category == category)
        if status_filter:
            query = query.filter(Maintenance.status == status_filter)
        if priority:
            query = query.filter(Maintenance.priority == priority)
        if request_type:
            query = query.filter(Maintenance.request_type == request_type)
        if assigned_technician:
            query = query.filter(Maintenance.assigned_technician.ilike(f"%{assigned_technician}%"))
        
        # Date range filtering
        if start_date or end_date:
            if start_date:
                query = query.filter(Maintenance.created_at >= datetime.fromisoformat(start_date))
            if end_date:
                query = query.filter(Maintenance.created_at <= datetime.fromisoformat(end_date))
        
        # Get total count
        total_count = query.count()
        
        # Apply ordering first, then pagination
        maintenance_records = query.order_by(Maintenance.created_at.desc()).offset(offset).limit(limit).all()
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        
        return {
            "maintenance": [record.to_dict() for record in maintenance_records],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": total_pages
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching maintenance records: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch maintenance records"
        )

@router.get("/stats")
async def get_maintenance_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get maintenance dashboard statistics.
    Equivalent to GET /stats in Node.js maintenance.ts
    """
    try:
        # Basic counts
        total_requests = db.query(Maintenance).count()
        pending_tasks = db.query(Maintenance).filter(
            Maintenance.status.in_([MaintenanceStatus.REQUESTED, MaintenanceStatus.SCHEDULED, MaintenanceStatus.IN_PROGRESS])
        ).count()
        
        # Completed in last 30 days
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        completed_last_30_days = db.query(Maintenance).filter(
            and_(
                Maintenance.status == MaintenanceStatus.COMPLETED,
                Maintenance.updated_at >= thirty_days_ago
            )
        ).count()
        
        # High priority tasks
        high_priority_tasks = db.query(Maintenance).filter(
            and_(
                Maintenance.priority.in_([Priority.CRITICAL, Priority.HIGH]),
                Maintenance.status != MaintenanceStatus.COMPLETED
            )
        ).count()
        
        # Status breakdown
        status_breakdown = db.query(
            Maintenance.status,
            func.count(Maintenance.asset_tag).label('count')
        ).group_by(Maintenance.status).all()
        
        # Priority breakdown
        priority_breakdown = db.query(
            Maintenance.priority,
            func.count(Maintenance.asset_tag).label('count')
        ).group_by(Maintenance.priority).all()
        
        # Category breakdown
        category_breakdown = db.query(
            Maintenance.category,
            func.count(Maintenance.asset_tag).label('count')
        ).group_by(Maintenance.category).all()
        
        # Request type breakdown
        request_type_breakdown = db.query(
            Maintenance.request_type,
            func.count(Maintenance.asset_tag).label('count')
        ).group_by(Maintenance.request_type).all()
        
        # Overdue maintenance
        overdue_tasks = db.query(Maintenance).filter(
            and_(
                Maintenance.scheduled_date < datetime.utcnow(),
                Maintenance.status != MaintenanceStatus.COMPLETED
            )
        ).count()
        
        # Due this week
        next_week = datetime.utcnow() + timedelta(days=7)
        due_this_week = db.query(Maintenance).filter(
            and_(
                Maintenance.scheduled_date.between(datetime.utcnow(), next_week),
                Maintenance.status != MaintenanceStatus.COMPLETED
            )
        ).count()
        
        # Assets under maintenance count
        under_maintenance_count = db.query(Maintenance).filter(
            Maintenance.status.in_([MaintenanceStatus.IN_PROGRESS, MaintenanceStatus.TESTING])
        ).count()
        
        # Calculate percentage
        under_maintenance_percentage = round((under_maintenance_count / total_requests) * 100) if total_requests > 0 else 0
        
        return {
            "totalRequests": total_requests,
            "pendingTasks": pending_tasks,
            "completedLast30Days": completed_last_30_days,
            "highPriorityTasks": high_priority_tasks,
            "overdueTasks": overdue_tasks,
            "dueThisWeek": due_this_week,
            "underMaintenanceCount": under_maintenance_count,
            "underMaintenancePercentage": under_maintenance_percentage,
            "statusBreakdown": [{"status": str(sb.status), "count": sb.count} for sb in status_breakdown],
            "priorityBreakdown": [{"priority": str(pb.priority), "count": pb.count} for pb in priority_breakdown],
            "categoryBreakdown": [{"category": str(cb.category), "count": cb.count} for cb in category_breakdown],
            "requestTypeBreakdown": [{"requestType": str(rtb.request_type), "count": rtb.count} for rtb in request_type_breakdown]
        }
        
    except Exception as e:
        logger.error(f"Error fetching maintenance stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch maintenance statistics"
        )

@router.get("/recent-activity")
async def get_recent_maintenance_activity(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get recent maintenance activity.
    Equivalent to GET /recent-activity in Node.js maintenance.ts
    """
    try:
        ten_days_ago = datetime.utcnow() - timedelta(days=10)
        
        recent_activity = db.query(Maintenance).filter(
            Maintenance.created_at >= ten_days_ago
        ).order_by(Maintenance.created_at.desc()).limit(20).all()
        
        # Group by activity type
        requested = [item for item in recent_activity if item.created_at >= ten_days_ago and item.status == MaintenanceStatus.REQUESTED]
        scheduled = [item for item in recent_activity if item.status == MaintenanceStatus.SCHEDULED and item.updated_at >= ten_days_ago]
        completed = [item for item in recent_activity if item.status == MaintenanceStatus.COMPLETED and item.updated_at >= ten_days_ago]
        
        return {
            "recentActivity": [record.to_dict() for record in recent_activity],
            "summary": {
                "requested": len(requested),
                "scheduled": len(scheduled),
                "completed": len(completed),
                "total": len(recent_activity)
            },
            "activities": {
                "requested": [record.to_dict() for record in requested],
                "scheduled": [record.to_dict() for record in scheduled],
                "completed": [record.to_dict() for record in completed]
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching recent maintenance activity: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch recent maintenance activity"
        )

@router.get("/{asset_tag}")
async def get_maintenance_record(
    asset_tag: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get specific maintenance record by asset tag.
    Equivalent to GET /:assetTag in Node.js maintenance.ts
    """
    try:
        maintenance = db.query(Maintenance).filter(Maintenance.asset_tag == asset_tag).first()
        
        if not maintenance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Maintenance record not found"
            )
        
        return maintenance.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching maintenance record: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch maintenance record"
        )

@router.post("/")
async def create_maintenance_record(
    maintenance_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create new maintenance request with workflow management.
    Equivalent to POST / in Node.js maintenance.ts
    Now includes automatic asset status transition to 'Under Maintenance'.
    """
    try:
        asset_tag = maintenance_data.get("assetTag") or maintenance_data.get("asset_tag")
        if not asset_tag:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Missing asset_tag or assetTag field"
            )
        
        # Check if asset exists and get it by asset_id (asset_tag)
        asset = db.query(Asset).filter(Asset.asset_id == asset_tag).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Asset with asset_id {asset_tag} not found"
            )
        
        # Check if maintenance record already exists for this asset
        existing = db.query(Maintenance).filter(
            and_(
                Maintenance.asset_tag == asset_tag,
                Maintenance.status.in_([
                    MaintenanceStatus.REQUESTED,
                    MaintenanceStatus.SCHEDULED,
                    MaintenanceStatus.IN_PROGRESS,
                    MaintenanceStatus.TESTING
                ])
            )
        ).first()
        
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Active maintenance record already exists for asset {asset_tag}"
            )
        
        # Check if asset is available for maintenance
        status_transition_attempted = asset.status in ["Ready", "Failed"]
        
        # Create new maintenance record
        maintenance = Maintenance(
            asset_tag=asset_tag,
            category=maintenance_data.get("category"),
            name=maintenance_data.get("name"),
            request_type=maintenance_data.get("requestType"),
            priority=maintenance_data.get("priority", Priority.MEDIUM),
            assigned_technician=maintenance_data.get("assignedTechnician"),
            description=maintenance_data.get("description"),
            scheduled_date=datetime.fromisoformat(maintenance_data["scheduledDate"]) if maintenance_data.get("scheduledDate") else None,
            scheduled_frequency=maintenance_data.get("scheduledFrequency"),
            estimated_hours=maintenance_data.get("estimatedHours"),
            estimated_cost=maintenance_data.get("estimatedCost"),
            notes=maintenance_data.get("notes"),
            status=maintenance_data.get("status", MaintenanceStatus.REQUESTED)
        )
        
        db.add(maintenance)
        db.flush()  # Get the maintenance record ID
        
        # Initialize workflow service and attempt to transition asset status
        workflow_service = AssetWorkflowService(db)
        transition_message = None
        
        if status_transition_attempted:
            # Transition asset status to Under Maintenance
            transition_success, transition_message = workflow_service.transition_asset_status(
                asset_id=asset.id,
                to_status="Under Maintenance",
                workflow_module=WorkflowModule.MAINTENANCE,
                user_id=current_user.id,
                change_reason=f"Maintenance record created: {maintenance.name}",
                notes=f"Maintenance priority: {maintenance.priority.value}, Type: {maintenance.request_type.value}"
            )
            
            if not transition_success:
                logger.warning(f"Failed to update asset status for {asset_tag}: {transition_message}")
        else:
            transition_message = f"Asset in {asset.status.value} status - maintenance record created without status change"
        
        db.commit()
        db.refresh(maintenance)
        
        result = maintenance.to_dict()
        result["asset_status"] = asset.status.value
        if transition_message:
            result["transition_message"] = transition_message
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating maintenance record: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create maintenance record"
        )

@router.put("/{asset_tag}")
async def update_maintenance_record(
    asset_tag: str,
    maintenance_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update maintenance record.
    Equivalent to PUT /:assetTag in Node.js maintenance.ts
    """
    try:
        maintenance = db.query(Maintenance).filter(Maintenance.asset_tag == asset_tag).first()
        if not maintenance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Maintenance record not found"
            )
        
        # Update fields
        for field, value in maintenance_data.items():
            if hasattr(maintenance, field) and value is not None:
                if field == "scheduledDate" and value:
                    setattr(maintenance, "scheduled_date", datetime.fromisoformat(value))
                elif field in ["requestType", "scheduledFrequency", "estimatedHours", "estimatedCost"]:
                    # Convert camelCase to snake_case
                    snake_field = {
                        "requestType": "request_type",
                        "scheduledFrequency": "scheduled_frequency",
                        "estimatedHours": "estimated_hours",
                        "estimatedCost": "estimated_cost"
                    }.get(field, field)
                    setattr(maintenance, snake_field, value)
                else:
                    setattr(maintenance, field, value)
        
        db.commit()
        db.refresh(maintenance)
        
        return maintenance.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating maintenance record: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update maintenance record"
        )

@router.put("/{asset_tag}/status")
async def update_maintenance_status(
    asset_tag: str,
    status_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update maintenance status with automatic asset status management.
    Equivalent to PUT /:assetTag/status in Node.js maintenance.ts
    Now includes automatic asset status transition when maintenance is completed.
    """
    try:
        maintenance = db.query(Maintenance).filter(Maintenance.asset_tag == asset_tag).first()
        if not maintenance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Maintenance record not found"
            )
        
        # Get asset
        asset = db.query(Asset).filter(Asset.asset_id == asset_tag).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Asset with asset_id {asset_tag} not found"
            )
        
        new_status = status_data.get("status")
        old_status = maintenance.status
        
        # Update status and related fields
        maintenance.status = new_status
        if status_data.get("notes") is not None:
            maintenance.notes = status_data.get("notes")
        if status_data.get("assignedTechnician") is not None:
            maintenance.assigned_technician = status_data.get("assignedTechnician")
        
        # Initialize workflow service for asset status transitions
        workflow_service = AssetWorkflowService(db)
        transition_message = None
        
        # Handle asset status transitions based on maintenance status
        if new_status == MaintenanceStatus.COMPLETED and old_status != MaintenanceStatus.COMPLETED:
            # Maintenance completed - transition asset back to Ready
            transition_success, transition_message = workflow_service.transition_asset_status(
                asset_id=asset.id,
                to_status="Ready",
                workflow_module=WorkflowModule.MAINTENANCE,
                user_id=current_user.id,
                change_reason="Maintenance completed",
                notes=f"Maintenance completed: {maintenance.name}"
            )
            
            if transition_success:
                # Update asset maintenance dates
                asset.last_maintenance = datetime.utcnow()
                if maintenance.scheduled_frequency and maintenance.scheduled_frequency != ScheduledFrequency.ONCE:
                    # Calculate next maintenance date based on frequency
                    if maintenance.scheduled_frequency == ScheduledFrequency.WEEKLY:
                        asset.next_maintenance = datetime.utcnow() + timedelta(weeks=1)
                    elif maintenance.scheduled_frequency == ScheduledFrequency.MONTHLY:
                        asset.next_maintenance = datetime.utcnow() + timedelta(days=30)
                    elif maintenance.scheduled_frequency == ScheduledFrequency.QUARTERLY:
                        asset.next_maintenance = datetime.utcnow() + timedelta(days=90)
                    elif maintenance.scheduled_frequency == ScheduledFrequency.HALF_YEARLY:
                        asset.next_maintenance = datetime.utcnow() + timedelta(days=180)
                    elif maintenance.scheduled_frequency == ScheduledFrequency.YEARLY:
                        asset.next_maintenance = datetime.utcnow() + timedelta(days=365)
            else:
                logger.warning(f"Failed to update asset status to Ready for {asset_tag}: {transition_message}")
        
        elif new_status == MaintenanceStatus.IN_PROGRESS and asset.status != "Under Maintenance":
            # Maintenance started - ensure asset is in maintenance status
            transition_success, transition_message = workflow_service.transition_asset_status(
                asset_id=asset.id,
                to_status="Under Maintenance",
                workflow_module=WorkflowModule.MAINTENANCE,
                user_id=current_user.id,
                change_reason="Maintenance in progress",
                notes=f"Maintenance started: {maintenance.name}"
            )
            
            if not transition_success:
                logger.warning(f"Failed to update asset status to Under Maintenance for {asset_tag}: {transition_message}")
        
        db.commit()
        db.refresh(maintenance)
        
        result = maintenance.to_dict()
        result["asset_status"] = asset.status.value
        if transition_message:
            result["transition_message"] = transition_message
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating maintenance status: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update maintenance status"
        )

@router.put("/{asset_tag}/schedule")
async def schedule_maintenance(
    asset_tag: str,
    schedule_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Schedule maintenance.
    Equivalent to PUT /:assetTag/schedule in Node.js maintenance.ts
    """
    try:
        maintenance = db.query(Maintenance).filter(Maintenance.asset_tag == asset_tag).first()
        if not maintenance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Maintenance record not found"
            )
        
        # Update scheduling information
        maintenance.status = MaintenanceStatus.SCHEDULED
        maintenance.scheduled_date = datetime.fromisoformat(schedule_data["scheduledDate"])
        
        if schedule_data.get("assignedTechnician"):
            maintenance.assigned_technician = schedule_data.get("assignedTechnician")
        if schedule_data.get("estimatedHours") is not None:
            maintenance.estimated_hours = schedule_data.get("estimatedHours")
        if schedule_data.get("estimatedCost") is not None:
            maintenance.estimated_cost = schedule_data.get("estimatedCost")
        if schedule_data.get("notes"):
            maintenance.notes = schedule_data.get("notes")
        
        db.commit()
        db.refresh(maintenance)
        
        return maintenance.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error scheduling maintenance: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to schedule maintenance"
        )

@router.delete("/{asset_tag}")
async def delete_maintenance_record(
    asset_tag: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete maintenance record.
    Equivalent to DELETE /:assetTag in Node.js maintenance.ts
    """
    try:
        maintenance = db.query(Maintenance).filter(Maintenance.asset_tag == asset_tag).first()
        if not maintenance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Maintenance record not found"
            )
        
        db.delete(maintenance)
        db.commit()
        
        return {"message": "Maintenance record deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting maintenance record: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete maintenance record"
        )

@router.get("/overdue/list")
async def get_overdue_maintenance(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get overdue maintenance tasks.
    Equivalent to GET /overdue/list in Node.js maintenance.ts
    """
    try:
        overdue_tasks = db.query(Maintenance).filter(
            and_(
                Maintenance.scheduled_date < datetime.utcnow(),
                Maintenance.status != MaintenanceStatus.COMPLETED
            )
        ).order_by(Maintenance.scheduled_date).all()
        
        return [task.to_dict() for task in overdue_tasks]
        
    except Exception as e:
        logger.error(f"Error fetching overdue tasks: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch overdue tasks"
        )

@router.get("/categories/list")
async def get_maintenance_categories(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get unique categories.
    Equivalent to GET /categories/list in Node.js maintenance.ts
    """
    try:
        categories = db.query(Maintenance.category).distinct().order_by(Maintenance.category).all()
        return [category[0] for category in categories if category[0]]
        
    except Exception as e:
        logger.error(f"Error fetching categories: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch categories"
        )

@router.get("/technicians/list")
async def get_maintenance_technicians(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get unique technicians.
    Equivalent to GET /technicians/list in Node.js maintenance.ts
    """
    try:
        technicians = db.query(Maintenance.assigned_technician).filter(
            Maintenance.assigned_technician.isnot(None),
            Maintenance.assigned_technician != ""
        ).distinct().order_by(Maintenance.assigned_technician).all()
        
        return [technician[0] for technician in technicians if technician[0]]
        
    except Exception as e:
        logger.error(f"Error fetching technicians: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch technicians"
        )

@router.get("/available-assets")
async def get_available_assets_for_maintenance(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    search: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None, alias="type"),
    location: Optional[str] = Query(None),
    county: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get assets available for maintenance requests.
    Assets with status 'Ready' or 'Failed' are available for maintenance.
    """
    try:
        offset = (page - 1) * limit
        
        # Query assets that can be put into maintenance
        # Ready assets and Failed assets can be moved to maintenance
        available_statuses = ["Ready", "Failed"]
        
        query = db.query(Asset).filter(Asset.status.in_(available_statuses))
        
        # Apply search filters
        if search:
            search_filter = or_(
                Asset.asset_id.ilike(f"%{search}%"),
                Asset.type.ilike(f"%{search}%"),
                Asset.model.ilike(f"%{search}%"),
                Asset.serial_number.ilike(f"%{search}%"),
                Asset.location.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Apply specific filters
        if asset_type:
            query = query.filter(Asset.type == asset_type)
        if location:
            query = query.filter(Asset.location.ilike(f"%{location}%"))
        if county:
            query = query.filter(Asset.county == county)
        
        # Get total count
        total_count = query.count()
        
        # Apply ordering and pagination
        assets = query.order_by(Asset.created_at.desc()).offset(offset).limit(limit).all()
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        
        return {
            "assets": [asset.to_dict() for asset in assets],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": total_count,
                "pages": total_pages
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching available assets for maintenance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch available assets for maintenance"
        )

@router.post("/request", response_model=dict)
async def create_maintenance_request(
    maintenance_data: dict,  # Using dict for now to maintain compatibility
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new maintenance request and automatically update asset status to 'Under Maintenance'.
    This follows the workflow: User selects asset → Creates maintenance request → Asset status changes to maintenance.
    """
    try:
        # Validate required fields
        required_fields = ["asset_tag", "category", "name", "request_type"]
        for field in required_fields:
            if field not in maintenance_data:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Missing required field: {field}"
                )
        
        asset_tag = maintenance_data["asset_tag"]
        
        # Check if asset exists and get it by asset_id (asset_tag)
        asset = db.query(Asset).filter(Asset.asset_id == asset_tag).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Asset with asset_id {asset_tag} not found"
            )
        
        # Check if asset is available for maintenance
        if asset.status not in ["Ready", "Failed"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Asset {asset_tag} is not available for maintenance. Current status: {asset.status}"
            )
        
        # Check if maintenance record already exists for this asset
        existing_maintenance = db.query(Maintenance).filter(
            and_(
                Maintenance.asset_tag == asset_tag,
                Maintenance.status.in_([
                    MaintenanceStatus.REQUESTED,
                    MaintenanceStatus.SCHEDULED,
                    MaintenanceStatus.IN_PROGRESS,
                    MaintenanceStatus.TESTING
                ])
            )
        ).first()
        
        if existing_maintenance:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Active maintenance request already exists for asset {asset_tag}"
            )
        
        # Create maintenance record
        maintenance = Maintenance(
            asset_tag=asset_tag,
            category=MaintenanceCategory(maintenance_data["category"]),
            name=maintenance_data["name"],
            request_type=RequestType(maintenance_data["request_type"]),
            priority=Priority(maintenance_data.get("priority", Priority.MEDIUM)),
            assigned_technician=maintenance_data.get("assigned_technician"),
            description=maintenance_data.get("description"),
            scheduled_date=datetime.fromisoformat(maintenance_data["scheduled_date"]) if maintenance_data.get("scheduled_date") else None,
            scheduled_frequency=ScheduledFrequency(maintenance_data["scheduled_frequency"]) if maintenance_data.get("scheduled_frequency") else None,
            estimated_hours=maintenance_data.get("estimated_hours"),
            estimated_cost=maintenance_data.get("estimated_cost"),
            notes=maintenance_data.get("notes"),
            status=MaintenanceStatus.REQUESTED
        )
        
        db.add(maintenance)
        db.flush()  # Get the maintenance record ID
        
        # Initialize workflow service and transition asset status to "Under Maintenance"
        workflow_service = AssetWorkflowService(db)
        
        # Transition asset status to Under Maintenance
        transition_success, transition_message = workflow_service.transition_asset_status(
            asset_id=asset.id,
            to_status="Under Maintenance",
            workflow_module=WorkflowModule.MAINTENANCE,
            user_id=current_user.id,
            change_reason=f"Maintenance request created: {maintenance.name}",
            notes=f"Maintenance priority: {maintenance.priority.value}, Type: {maintenance.request_type.value}"
        )
        
        if not transition_success:
            # Rollback maintenance creation if asset status transition fails
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to update asset status: {transition_message}"
            )
        
        db.commit()
        
        logger.info(f"Maintenance request created for asset {asset_tag} by user {current_user.id}")
        
        return {
            "message": "Maintenance request created successfully",
            "maintenance": maintenance.to_dict(),
            "asset_status": "Under Maintenance"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating maintenance request: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create maintenance request"
        )

@router.put("/{asset_tag}/complete")
async def complete_maintenance(
    asset_tag: str,
    completion_data: dict,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Complete maintenance and automatically update asset status back to 'Ready'.
    This follows the workflow: Maintenance completed → Asset status changes to ready.
    """
    try:
        # Get maintenance record
        maintenance = db.query(Maintenance).filter(Maintenance.asset_tag == asset_tag).first()
        if not maintenance:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Maintenance record not found for asset {asset_tag}"
            )
        
        # Check if maintenance can be completed
        if maintenance.status == MaintenanceStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Maintenance is already completed"
            )
        
        # Get asset
        asset = db.query(Asset).filter(Asset.asset_id == asset_tag).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Asset with asset_id {asset_tag} not found"
            )
        
        # Update maintenance record
        maintenance.status = MaintenanceStatus.COMPLETED
        if completion_data.get("notes"):
            maintenance.notes = completion_data["notes"]
        if completion_data.get("assigned_technician"):
            maintenance.assigned_technician = completion_data["assigned_technician"]
        
        # Initialize workflow service and transition asset status back to "Ready"
        workflow_service = AssetWorkflowService(db)
        
        # Transition asset status to Ready
        transition_success, transition_message = workflow_service.transition_asset_status(
            asset_id=asset.id,
            to_status="Ready",
            workflow_module=WorkflowModule.MAINTENANCE,
            user_id=current_user.id,
            change_reason="Maintenance completed",
            notes=f"Maintenance completed: {maintenance.name}"
        )
        
        if not transition_success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to update asset status: {transition_message}"
            )
        
        # Update asset maintenance dates
        asset.last_maintenance = datetime.utcnow()
        if maintenance.scheduled_frequency and maintenance.scheduled_frequency != ScheduledFrequency.ONCE:
            # Calculate next maintenance date based on frequency
            if maintenance.scheduled_frequency == ScheduledFrequency.WEEKLY:
                asset.next_maintenance = datetime.utcnow() + timedelta(weeks=1)
            elif maintenance.scheduled_frequency == ScheduledFrequency.MONTHLY:
                asset.next_maintenance = datetime.utcnow() + timedelta(days=30)
            elif maintenance.scheduled_frequency == ScheduledFrequency.QUARTERLY:
                asset.next_maintenance = datetime.utcnow() + timedelta(days=90)
            elif maintenance.scheduled_frequency == ScheduledFrequency.HALF_YEARLY:
                asset.next_maintenance = datetime.utcnow() + timedelta(days=180)
            elif maintenance.scheduled_frequency == ScheduledFrequency.YEARLY:
                asset.next_maintenance = datetime.utcnow() + timedelta(days=365)
        
        db.commit()
        
        logger.info(f"Maintenance completed for asset {asset_tag} by user {current_user.id}")
        
        return {
            "message": "Maintenance completed successfully",
            "maintenance": maintenance.to_dict(),
            "asset_status": "Ready",
            "asset": asset.to_dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error completing maintenance: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to complete maintenance"
        )

