#!/usr/bin/env python3
"""
Fix Assets Table Enum Values
This script will recreate the assets table with proper enum values.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
from app.models.assets import AssetStatus, AssetCondition
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_assets_enum_values():
    """Fix the assets table enum values to match Python enums."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.begin() as conn:  # Use begin() for auto-commit
            logger.info("🔍 Checking current assets table...")
            
            # First backup any existing data
            result = conn.execute(text("SELECT COUNT(*) FROM assets"))
            count = result.scalar()
            logger.info(f"Found {count} existing assets")
            
            if count > 0:
                logger.info("⚠️  Backing up existing assets...")
                conn.execute(text("""
                    CREATE TABLE IF NOT EXISTS assets_backup AS 
                    SELECT * FROM assets
                """))
                logger.info("✅ Backup created")
            
            # Get the status and condition enum values from Python
            status_values = [status.value for status in AssetStatus]
            condition_values = [condition.value for condition in AssetCondition]
            
            logger.info(f"Status values: {status_values}")
            logger.info(f"Condition values: {condition_values}")
            
            # Create enum strings for MySQL
            status_enum = "ENUM('" + "','".join(status_values) + "')"
            condition_enum = "ENUM('" + "','".join(condition_values) + "')"
            
            logger.info("🔧 Updating enum columns...")
            
            # Update the status column
            logger.info("Updating status column...")
            conn.execute(text(f"""
                ALTER TABLE assets 
                MODIFY COLUMN status {status_enum} NOT NULL DEFAULT 'New'
            """))
            
            # Update the condition column
            logger.info("Updating condition column...")
            conn.execute(text(f"""
                ALTER TABLE assets 
                MODIFY COLUMN `condition` {condition_enum} NOT NULL DEFAULT 'Good'
            """))
            
            logger.info("✅ Successfully updated assets table enum values")
            
            # Show the updated table structure
            result = conn.execute(text("SHOW CREATE TABLE assets"))
            table_def = result.fetchone()[1]
            logger.info("Updated table definition:")
            print(table_def)
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    fix_assets_enum_values()
