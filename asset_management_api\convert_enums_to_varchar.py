#!/usr/bin/env python3
"""
Convert ENUM columns to VARCHAR to allow flexible user input
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def convert_enums_to_varchar():
    """Convert status and condition columns from ENUM to VARCHAR."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            logger.info("🔄 Converting ENUM columns to VARCHAR...")
            
            # Check current column types
            logger.info("📊 Checking current column types...")
            result = conn.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'assets' 
                AND COLUMN_NAME IN ('status', 'condition')
            """))
            
            current_types = {}
            for row in result.fetchall():
                current_types[row[0]] = {
                    'data_type': row[1],
                    'column_type': row[2]
                }
                logger.info(f"   {row[0]}: {row[1]} ({row[2]})")
            
            # Convert status column to VARCHAR
            if 'status' in current_types:
                logger.info("🔧 Converting status column to VARCHAR(50)...")
                conn.execute(text("""
                    ALTER TABLE assets 
                    MODIFY COLUMN status VARCHAR(50) NOT NULL DEFAULT 'New'
                """))
                logger.info("   ✅ Status column converted to VARCHAR(50)")
            
            # Convert condition column to VARCHAR
            if 'condition' in current_types:
                logger.info("🔧 Converting condition column to VARCHAR(50)...")
                conn.execute(text("""
                    ALTER TABLE assets 
                    MODIFY COLUMN `condition` VARCHAR(50) NOT NULL DEFAULT 'Good'
                """))
                logger.info("   ✅ Condition column converted to VARCHAR(50)")
            
            # Commit the changes
            conn.commit()
            
            logger.info("\n🔍 Verifying the changes...")
            result = conn.execute(text("""
                SELECT COLUMN_NAME, DATA_TYPE, COLUMN_TYPE 
                FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'assets' 
                AND COLUMN_NAME IN ('status', 'condition')
            """))
            
            for row in result.fetchall():
                logger.info(f"   {row[0]}: {row[1]} ({row[2]})")
            
            # Check current data values
            logger.info("\n📊 Current status values in database:")
            result = conn.execute(text("SELECT DISTINCT status FROM assets ORDER BY status"))
            status_values = [row[0] for row in result.fetchall()]
            logger.info(f"   {status_values}")
            
            logger.info("\n📊 Current condition values in database:")
            result = conn.execute(text("SELECT DISTINCT `condition` FROM assets ORDER BY `condition`"))
            condition_values = [row[0] for row in result.fetchall()]
            logger.info(f"   {condition_values}")
            
            logger.info("\n✅ ENUM to VARCHAR conversion completed successfully!")
            logger.info("🎉 Users can now enter any custom status and condition values!")
            
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    convert_enums_to_varchar()
