import React, { useEffect, useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { HelpCircle, LogOut, User, MapPin, Globe } from "lucide-react";
import { Link, useNavigate, useLocation } from "react-router-dom";
import { useAuth } from "@/App";
import { counties } from "@/data/mock-data";
import { Breadcrumb } from "./Breadcrumb";

export function TopBar() {
  const navigate = useNavigate();
  const { userCounty, accessLevel } = useAuth();
  const [user, setUser] = useState<{
    username: string;
    role: string;
    county?: string;
    accessLevel?: 'county' | 'state';
  } | null>(null);

  useEffect(() => {
    // Get user info from localStorage
    const userStr = localStorage.getItem("user");
    if (userStr) {
      try {
        const userData = JSON.parse(userStr);
        setUser(userData);
      } catch (e) {
        console.error("Error parsing user data:", e);
      }
    }
  }, []);

  const handleLogout = () => {
    // Clear user data from localStorage
    localStorage.removeItem("isLoggedIn");
    localStorage.removeItem("user");

    // Redirect to login page
    navigate("/login");
  };
  const location = useLocation();
  const pathSegments = location.pathname.split('/').filter(Boolean);

  // Skip rendering breadcrumb on home/dashboard
  const showBreadcrumb = !(pathSegments.length === 0 || (pathSegments.length === 1 && pathSegments[0] === 'dashboard'));

  return (
    <div className="border-b flex flex-col bg-white sticky top-0 z-20 shadow-sm">
      <div className="h-16 flex items-center justify-between px-4 md:px-6">
        <Link to="/" className="flex items-center">
          <h1 className="text-lg md:text-xl font-semibold text-zenith-blue-dark">
            Asset Management
          </h1>
        </Link>

        <div className="flex items-center gap-2 md:gap-4">
          <Button variant="ghost" size="sm" className="hidden md:flex items-center gap-1 text-gray-600 hover:text-blue-600">
            <HelpCircle className="h-4 w-4" />
            <span className="text-sm">Help</span>
          </Button>

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative flex items-center gap-2 hover:bg-gray-100">
                <Avatar className="h-8 w-8 border-2 border-gray-200">
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-blue-600 text-white">SC</AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium">{user?.username || "Admin User"}</div>
                  <div className="text-xs text-gray-500 flex items-center">
                    {user?.accessLevel === 'state' ? (
                      <>
                        <Globe className="h-3 w-3 mr-1" />
                        <span>{user?.role}</span>
                      </>
                    ) : user?.county ? (
                      <>
                        <MapPin className="h-3 w-3 mr-1" />
                        <span>
                          {counties.find(c => c.id === user.county)?.name || user.county} {user?.role}
                        </span>
                      </>
                    ) : (
                      <span>{user?.role || "Administrator"}</span>
                    )}
                  </div>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="py-2 cursor-pointer">
                <User className="h-4 w-4 mr-2" />
                <Link to="/profile" className="flex-1">Profile</Link>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                className="py-2 cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleLogout}
              >
                <LogOut className="h-4 w-4 mr-2" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Breadcrumb row - only shown when needed */}
      {showBreadcrumb && (
        <div className="flex justify-end px-4 md:px-6 py-2 bg-gray-50 border-t border-gray-100">
          <Breadcrumb />
        </div>
      )}
    </div>
  );
}
