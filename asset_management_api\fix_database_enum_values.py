#!/usr/bin/env python3
"""
Fix database enum values to match Python model
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
from app.models.assets import AssetStatus, AssetCondition
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_database_enum_values():
    """Fix database enum values to match Python model."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            logger.info("🔧 Fixing database enum values to match Python model...")

            # First, check what data exists
            result = conn.execute(text("SELECT DISTINCT status FROM assets"))
            existing_statuses = [row[0] for row in result.fetchall()]
            logger.info(f"Existing status values in database: {existing_statuses}")
            
            result = conn.execute(text("SELECT DISTINCT `condition` FROM assets"))
            existing_conditions = [row[0] for row in result.fetchall()]
            logger.info(f"Existing condition values in database: {existing_conditions}")
            
            # Map old values to new values
            status_mapping = {
                "New": "NEW",
                "Ready": "READY", 
                "Failed": "FAILED",
                "Packed": "PACKED",
                "Checked-out": "CHECKED_OUT",
                "In-transfer": "IN_TRANSFER",
                "Delivered": "DELIVERED",
                "Using": "USING",
                "Damaged": "DAMAGED",
                "Under Maintenance": "UNDER_MAINTENANCE",
                "Completed": "COMPLETED",
                "Retired": "RETIRED"
            }
            
            condition_mapping = {
                "Excellent": "EXCELLENT",
                "Good": "GOOD",
                "Fair": "FAIR", 
                "Poor": "POOR",
                "Damaged": "DAMAGED"
            }
            
            # Update existing data to use new enum values
            logger.info("📝 Updating existing data...")
            
            for old_val, new_val in status_mapping.items():
                if old_val in existing_statuses:
                    conn.execute(text(f"UPDATE assets SET status = '{new_val}' WHERE status = '{old_val}'"))
                    logger.info(f"  ✓ Updated status '{old_val}' → '{new_val}'")
            
            for old_val, new_val in condition_mapping.items():
                if old_val in existing_conditions:
                    conn.execute(text(f"UPDATE assets SET `condition` = '{new_val}' WHERE `condition` = '{old_val}'"))
                    logger.info(f"  ✓ Updated condition '{old_val}' → '{new_val}'")
            
            # Now update the enum definitions in the database
            logger.info("🔧 Updating database enum definitions...")
            
            # Get enum values from Python model
            status_values = [status.value for status in AssetStatus]
            condition_values = [condition.value for condition in AssetCondition]
            
            # Update status enum
            status_enum_def = "','".join(status_values)
            conn.execute(text(f"ALTER TABLE assets MODIFY COLUMN status ENUM('{status_enum_def}') NOT NULL DEFAULT 'NEW'"))
            logger.info(f"  ✓ Updated status enum: {status_values}")
            
            # Update condition enum  
            condition_enum_def = "','".join(condition_values)
            conn.execute(text(f"ALTER TABLE assets MODIFY COLUMN `condition` ENUM('{condition_enum_def}') NOT NULL DEFAULT 'GOOD'"))
            logger.info(f"  ✓ Updated condition enum: {condition_values}")
            
            # Commit all changes
            conn.commit()
            logger.info("✅ Database enum values updated successfully!")
            
            # Verify the changes
            logger.info("🔍 Verifying changes...")
            result = conn.execute(text("SELECT DISTINCT status FROM assets"))
            new_statuses = [row[0] for row in result.fetchall()]
            logger.info(f"New status values: {new_statuses}")
            
            result = conn.execute(text("SELECT DISTINCT `condition` FROM assets"))
            new_conditions = [row[0] for row in result.fetchall()]
            logger.info(f"New condition values: {new_conditions}")
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    fix_database_enum_values()
