# app/schemas/assets.py
from pydantic import BaseModel, Field, validator
from typing import Optional, Dict, Any
from datetime import datetime
from app.models.assets import AssetDefaults

class AssetBase(BaseModel):
    asset_id: Optional[str] = Field(None, max_length=100)
    type: str = Field(..., min_length=1, max_length=255)
    category: Optional[str] = Field(None, max_length=255)
    model: Optional[str] = Field(None, max_length=255)
    serial_number: Optional[str] = Field(None, max_length=255)
    status: str = Field(default=AssetDefaults.DEFAULT_STATUS, max_length=50)
    condition: str = Field(default=AssetDefaults.DEFAULT_CONDITION, max_length=50)
    location: str = Field(..., min_length=1, max_length=255)
    assigned_to: Optional[str] = Field(None, max_length=255)

    # Geographical hierarchy for RBAC (optional for now)
    state: Optional[str] = Field(None, max_length=100)
    county: Optional[str] = Field(None, max_length=100)
    precinct: Optional[str] = Field(None, max_length=100)
    purchase_date: Optional[datetime] = None
    warranty_expiry: Optional[datetime] = None
    last_maintenance: Optional[datetime] = None
    next_maintenance: Optional[datetime] = None
    last_checked: Optional[datetime] = None
    notes: Optional[str] = None
    specifications: Optional[Dict[str, Any]] = None

class AssetCreate(AssetBase):
    pass

class AssetUpdate(BaseModel):
    asset_id: Optional[str] = Field(None, max_length=100)
    type: Optional[str] = Field(None, min_length=1, max_length=255)
    category: Optional[str] = Field(None, max_length=255)
    model: Optional[str] = Field(None, max_length=255)
    serial_number: Optional[str] = Field(None, max_length=255)
    status: Optional[str] = Field(None, max_length=50)
    condition: Optional[str] = Field(None, max_length=50)
    location: Optional[str] = Field(None, min_length=1, max_length=255)
    assigned_to: Optional[str] = Field(None, max_length=255)
    county: Optional[str] = Field(None, max_length=100)
    precinct: Optional[str] = Field(None, max_length=100)
    purchase_date: Optional[datetime] = None
    warranty_expiry: Optional[datetime] = None
    last_maintenance: Optional[datetime] = None
    next_maintenance: Optional[datetime] = None
    last_checked: Optional[datetime] = None
    notes: Optional[str] = None
    specifications: Optional[Dict[str, Any]] = None

class AssetResponse(AssetBase):
    id: int
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class AssetStatusUpdate(BaseModel):
    status: str = Field(..., max_length=50)
    location: Optional[str] = None
    assigned_to: Optional[str] = None
    notes: Optional[str] = None

class AssetConditionUpdate(BaseModel):
    condition: str = Field(..., max_length=50)
    notes: Optional[str] = None

class AssetSearch(BaseModel):
    search: Optional[str] = None
    type: Optional[str] = None
    status: Optional[str] = None
    condition: Optional[str] = None
    location: Optional[str] = None
    county: Optional[str] = None
    precinct: Optional[str] = None
    page: int = Field(1, ge=1)
    limit: int = Field(50, ge=1, le=200)