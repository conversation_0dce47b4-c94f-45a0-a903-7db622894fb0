# app/routes/checkout_sessions.py
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from app.config.database import get_db
from app.models.checkout_sessions import CheckoutSession, CheckoutType, CheckoutStatus
from app.models.assets import Asset
from app.models.user import User
from app.middleware.auth import get_current_user, require_admin
from app.schemas.checkout_sessions import (
    CheckoutSessionCreate, CheckoutSessionUpdate, CheckoutSessionResponse
)
from app.services.asset_workflow_service import AssetWorkflowService, WorkflowModules
from typing import Optional, List
import logging
from datetime import datetime, timedelta
import uuid

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/", response_model=CheckoutSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_checkout_session(
    checkout_data: CheckoutSessionCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new checkout session.
    Updates asset status to CHECKED_OUT.
    """
    try:
        # Validate asset exists and is in correct status
        asset = db.query(Asset).filter(Asset.id == checkout_data.asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        # Check if asset can be checked out (should be PACKED)
        workflow_service = AssetWorkflowService(db)
        can_transition, message = workflow_service.can_transition(
            asset_id=checkout_data.asset_id,
            to_status="Checked-out",
            workflow_module=WorkflowModules.CHECKOUT,
            user_id=current_user.id,
            context={
                "checkout_type": checkout_data.checkout_type.value,
                "personnel_name": checkout_data.personnel_name,
                "to_location": checkout_data.to_location
            }
        )
        
        if not can_transition:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot checkout asset: {message}"
            )
        
        # Generate unique checkout ID
        checkout_id = f"CO-{datetime.now().strftime('%Y%m%d')}-{str(uuid.uuid4())[:8].upper()}"
        
        # Create checkout session
        checkout_session = CheckoutSession(
            checkout_id=checkout_id,
            asset_id=checkout_data.asset_id,
            checkout_type=checkout_data.checkout_type,
            status=CheckoutStatus.ACTIVE,
            checked_out_by=current_user.id,
            personnel_name=checkout_data.personnel_name,
            personnel_email=checkout_data.personnel_email,
            personnel_phone=checkout_data.personnel_phone,
            personnel_id=checkout_data.personnel_id,
            from_location=checkout_data.from_location,
            to_location=checkout_data.to_location,
            county=checkout_data.county,
            precinct=checkout_data.precinct,
            purpose=checkout_data.purpose,
            expected_return_date=checkout_data.expected_return_date,
            condition_at_checkout=checkout_data.condition_at_checkout,
            authorized_by=checkout_data.authorized_by,
            authorization_code=checkout_data.authorization_code,
            checkout_notes=checkout_data.checkout_notes
        )
        
        db.add(checkout_session)
        db.flush()  # Get the ID
        
        # Update asset status to CHECKED_OUT
        success, transition_message = workflow_service.transition_asset_status(
            asset_id=checkout_data.asset_id,
            to_status="Checked-out",
            workflow_module=WorkflowModules.CHECKOUT,
            user_id=current_user.id,
            change_reason=f"Asset checked out: {checkout_id}",
            session_id=checkout_id,
            notes=f"Checked out to {checkout_data.personnel_name} for {checkout_data.purpose}",
            context={
                "checkout_id": checkout_id,
                "personnel_name": checkout_data.personnel_name,
                "to_location": checkout_data.to_location,
                "expected_return": checkout_data.expected_return_date.isoformat() if checkout_data.expected_return_date else None
            }
        )
        
        if not success:
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update asset status: {transition_message}"
            )
        
        # Update asset location
        asset.location = checkout_data.to_location
        asset.assigned_to = checkout_data.personnel_name
        
        db.commit()
        db.refresh(checkout_session)
        
        logger.info(f"Checkout session {checkout_id} created for asset {asset.asset_id} by user {current_user.id}")
        return checkout_session
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating checkout session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error creating checkout session"
        )

@router.get("/", response_model=dict)
async def get_checkout_sessions(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    asset_id: Optional[int] = Query(None),
    status: Optional[CheckoutStatus] = Query(None),
    checkout_type: Optional[CheckoutType] = Query(None),
    personnel_name: Optional[str] = Query(None),
    location: Optional[str] = Query(None),
    overdue_only: bool = Query(False, description="Show only overdue checkouts"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get checkout sessions with filtering and pagination."""
    try:
        # Calculate offset
        offset = (page - 1) * limit
        
        # Build query
        query = db.query(CheckoutSession)
        
        # Apply filters
        if asset_id:
            query = query.filter(CheckoutSession.asset_id == asset_id)
        if status:
            query = query.filter(CheckoutSession.status == status)
        if checkout_type:
            query = query.filter(CheckoutSession.checkout_type == checkout_type)
        if personnel_name:
            query = query.filter(CheckoutSession.personnel_name.ilike(f"%{personnel_name}%"))
        if location:
            query = query.filter(or_(
                CheckoutSession.from_location.ilike(f"%{location}%"),
                CheckoutSession.to_location.ilike(f"%{location}%")
            ))
        if overdue_only:
            query = query.filter(
                and_(
                    CheckoutSession.status == CheckoutStatus.ACTIVE,
                    CheckoutSession.expected_return_date < datetime.utcnow()
                )
            )
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination and ordering
        sessions = query.order_by(CheckoutSession.checkout_date.desc()).offset(offset).limit(limit).all()
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1
        
        return {
            "checkout_sessions": sessions,
            "pagination": {
                "page": page,
                "limit": limit,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching checkout sessions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error fetching checkout sessions"
        )

@router.get("/{checkout_id}", response_model=CheckoutSessionResponse)
async def get_checkout_session(
    checkout_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific checkout session by ID."""
    session = db.query(CheckoutSession).filter(CheckoutSession.checkout_id == checkout_id).first()
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Checkout session not found"
        )
    return session

@router.put("/{checkout_id}", response_model=CheckoutSessionResponse)
async def update_checkout_session(
    checkout_id: str,
    update_data: CheckoutSessionUpdate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update checkout session.
    Handles return processing and status changes.
    """
    try:
        # Get checkout session
        session = db.query(CheckoutSession).filter(CheckoutSession.checkout_id == checkout_id).first()
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Checkout session not found"
            )
        
        # Store previous status
        previous_status = session.status
        
        # Update fields
        for field, value in update_data.dict(exclude_unset=True).items():
            if hasattr(session, field):
                setattr(session, field, value)
        
        # Handle status changes
        if update_data.status and update_data.status != previous_status:
            if update_data.status == CheckoutStatus.COMPLETED:
                # Asset returned - update timestamps
                session.actual_return_date = update_data.actual_return_date or datetime.utcnow()
                session.completed_at = datetime.utcnow()
                session.returned_by = update_data.returned_by or current_user.id
                
                # Update asset status back to READY (assuming good condition)
                workflow_service = AssetWorkflowService(db)
                
                # Determine new asset status based on condition
                new_status = "Ready"
                if update_data.condition_at_return and "damaged" in update_data.condition_at_return.lower():
                    new_status = "Damaged"
                
                success, message = workflow_service.transition_asset_status(
                    asset_id=session.asset_id,
                    to_status=new_status,
                    workflow_module=WorkflowModules.CHECKOUT,
                    user_id=current_user.id,
                    change_reason=f"Asset returned from checkout: {checkout_id}",
                    session_id=checkout_id,
                    notes=update_data.return_notes or "Asset returned",
                    context={
                        "condition_at_return": update_data.condition_at_return,
                        "return_notes": update_data.return_notes
                    }
                )
                
                if not success:
                    logger.warning(f"Could not update asset status for returned checkout {checkout_id}: {message}")
            
            elif update_data.status == CheckoutStatus.EXTENDED:
                # Handle extension
                session.extension_count += 1
                session.last_extension_date = datetime.utcnow()
                if update_data.expected_return_date:
                    session.expected_return_date = update_data.expected_return_date
        
        db.commit()
        db.refresh(session)
        
        logger.info(f"Checkout session {checkout_id} updated by user {current_user.id}")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating checkout session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error updating checkout session"
        )

@router.post("/{checkout_id}/extend", response_model=CheckoutSessionResponse)
async def extend_checkout_session(
    checkout_id: str,
    new_return_date: datetime = Query(..., description="New expected return date"),
    extension_reason: str = Query(..., description="Reason for extension"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Extend checkout session return date."""
    try:
        # Get checkout session
        session = db.query(CheckoutSession).filter(CheckoutSession.checkout_id == checkout_id).first()
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Checkout session not found"
            )
        
        if session.status != CheckoutStatus.ACTIVE:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot extend inactive checkout session"
            )
        
        # Update extension details
        session.expected_return_date = new_return_date
        session.extension_reason = extension_reason
        session.extension_count += 1
        session.last_extension_date = datetime.utcnow()
        session.status = CheckoutStatus.EXTENDED
        
        db.commit()
        db.refresh(session)
        
        logger.info(f"Checkout session {checkout_id} extended by user {current_user.id}")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error extending checkout session: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error extending checkout session"
        )

@router.post("/{checkout_id}/return", response_model=CheckoutSessionResponse)
async def return_asset(
    checkout_id: str,
    condition_at_return: str = Query(..., description="Asset condition at return"),
    return_notes: Optional[str] = Query(None, description="Return notes"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Process asset return."""
    try:
        # Get checkout session
        session = db.query(CheckoutSession).filter(CheckoutSession.checkout_id == checkout_id).first()
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Checkout session not found"
            )
        
        if session.status == CheckoutStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Asset already returned"
            )
        
        # Update return details
        session.status = CheckoutStatus.COMPLETED
        session.actual_return_date = datetime.utcnow()
        session.condition_at_return = condition_at_return
        session.return_notes = return_notes
        session.returned_by = current_user.id
        session.return_verified_by = current_user.id
        session.completed_at = datetime.utcnow()
        
        # Update asset status
        workflow_service = AssetWorkflowService(db)
        new_status = "Ready"
        if "damaged" in condition_at_return.lower() or "poor" in condition_at_return.lower():
            new_status = "Damaged"
        
        success, message = workflow_service.transition_asset_status(
            asset_id=session.asset_id,
            to_status=new_status,
            workflow_module=WorkflowModules.CHECKOUT,
            user_id=current_user.id,
            change_reason=f"Asset returned from checkout: {checkout_id}",
            session_id=checkout_id,
            notes=return_notes or "Asset returned",
            context={
                "condition_at_return": condition_at_return,
                "return_notes": return_notes
            }
        )
        
        if not success:
            logger.warning(f"Could not update asset status for return {checkout_id}: {message}")
        
        db.commit()
        db.refresh(session)
        
        logger.info(f"Asset returned for checkout session {checkout_id} by user {current_user.id}")
        return session
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error processing asset return: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error processing return"
        )

@router.get("/overdue/list", response_model=List[CheckoutSessionResponse])
async def get_overdue_checkouts(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all overdue checkout sessions."""
    overdue_sessions = db.query(CheckoutSession).filter(
        and_(
            CheckoutSession.status.in_([CheckoutStatus.ACTIVE, CheckoutStatus.EXTENDED]),
            CheckoutSession.expected_return_date < datetime.utcnow()
        )
    ).order_by(CheckoutSession.expected_return_date.asc()).all()
    
    return overdue_sessions

@router.get("/asset/{asset_id}/history", response_model=List[CheckoutSessionResponse])
async def get_asset_checkout_history(
    asset_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get checkout history for a specific asset."""
    sessions = db.query(CheckoutSession).filter(
        CheckoutSession.asset_id == asset_id
    ).order_by(CheckoutSession.checkout_date.desc()).all()
    
    return sessions
