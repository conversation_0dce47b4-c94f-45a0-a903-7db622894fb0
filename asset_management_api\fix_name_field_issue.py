#!/usr/bin/env python3
"""
Fix the name field issue in assets table
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text, MetaData, Table
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_name_field():
    """Fix the name field issue in assets table."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            # First, let's see the current table structure
            logger.info("🔍 Checking current assets table structure...")

            try:
                result = conn.execute(text("DESCRIBE assets"))
                columns = result.fetchall()
                print("Current assets table columns:")
                for col in columns:
                    print(f"  {col[0]}: {col[1]} {'NULL' if col[2] == 'YES' else 'NOT NULL'} {f'DEFAULT {col[4]}' if col[4] else ''}")
                
                # Check if name column exists
                column_names = [col[0] for col in columns]
                if 'name' in column_names:
                    logger.info("❗ Found 'name' column in database table")
                    
                    # Option 1: Make name column nullable with a default value
                    logger.info("🔧 Making 'name' column nullable with default value...")
                    conn.execute(text("ALTER TABLE assets MODIFY COLUMN name VARCHAR(255) NULL DEFAULT NULL"))
                    
                    # Alternatively, if we want to drop the name column entirely:
                    # logger.info("🗑️ Dropping 'name' column from assets table...")
                    # conn.execute(text("ALTER TABLE assets DROP COLUMN name"))
                    
                    conn.commit()
                    logger.info("✅ Fixed 'name' column successfully")
                else:
                    logger.info("✅ No 'name' column found - table schema looks good")
                    
            except Exception as e:
                logger.error(f"❌ Error checking/fixing name column: {e}")
                conn.rollback()
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_name_field()
