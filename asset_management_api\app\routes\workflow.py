# app/routes/workflow.py
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from app.config.database import get_db
from app.models.assets import Asset
from app.models.asset_status_history import AssetStatusHistory
from app.models.workflow_state_transitions import WorkflowStateTransition, WorkflowModule
from app.models.user import User
from app.middleware.auth import get_current_user, require_admin
from app.schemas.workflow import (
    AssetStatusTransitionRequest, AssetStatusTransitionResponse,
    WorkflowStateTransitionResponse, AssetStatusHistoryResponse,
    ValidTransitionsResponse, WorkflowValidationRequest, WorkflowValidationResponse
)
from app.services.asset_workflow_service import AssetWorkflowService
from typing import Optional, List
import logging
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)

@router.post("/transition", response_model=AssetStatusTransitionResponse)
async def transition_asset_status(
    transition_request: AssetStatusTransitionRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Transition an asset's status with workflow validation.
    This is the central endpoint for all status changes.
    """
    try:
        workflow_service = AssetWorkflowService(db)
        
        # Get current asset status
        asset = db.query(Asset).filter(Asset.id == transition_request.asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        previous_status = asset.status.value if asset.status else "Unknown"
        
        # Perform transition
        success, message = workflow_service.transition_asset_status(
            asset_id=transition_request.asset_id,
            to_status=transition_request.to_status,
            workflow_module=transition_request.workflow_module,
            user_id=current_user.id,
            change_reason=transition_request.change_reason,
            session_id=transition_request.session_id,
            notes=transition_request.notes,
            context=transition_request.context or {}
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=message
            )
        
        return AssetStatusTransitionResponse(
            success=True,
            message=message,
            asset_id=transition_request.asset_id,
            previous_status=previous_status,
            new_status=transition_request.to_status,
            changed_by=current_user.id,
            changed_at=datetime.utcnow()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error transitioning asset status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during status transition"
        )

@router.post("/validate", response_model=WorkflowValidationResponse)
async def validate_transition(
    validation_request: WorkflowValidationRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Validate if an asset status transition is allowed without performing it.
    Useful for UI validation before submitting the actual transition.
    """
    try:
        workflow_service = AssetWorkflowService(db)
        
        # Get current asset status
        asset = db.query(Asset).filter(Asset.id == validation_request.asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        current_status = asset.status.value if asset.status else "Unknown"
        
        # Validate transition
        can_transition, message = workflow_service.can_transition(
            asset_id=validation_request.asset_id,
            to_status=validation_request.to_status,
            workflow_module=validation_request.workflow_module,
            user_id=current_user.id,
            context=validation_request.context or {}
        )
        
        return WorkflowValidationResponse(
            can_transition=can_transition,
            message=message,
            asset_id=validation_request.asset_id,
            current_status=current_status,
            target_status=validation_request.to_status,
            workflow_module=validation_request.workflow_module.value
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating transition: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during validation"
        )

@router.get("/asset/{asset_id}/valid-transitions", response_model=ValidTransitionsResponse)
async def get_valid_transitions(
    asset_id: int,
    workflow_module: WorkflowModule = Query(..., description="Workflow module context"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all valid transitions for an asset given its current status and workflow context.
    """
    try:
        # Get asset
        asset = db.query(Asset).filter(Asset.id == asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        current_status = asset.status.value if asset.status else "Unknown"
        
        # Get valid transitions
        workflow_service = AssetWorkflowService(db)
        valid_transitions = workflow_service.get_valid_transitions(current_status, workflow_module)
        
        return ValidTransitionsResponse(
            asset_id=asset_id,
            current_status=current_status,
            valid_transitions=valid_transitions
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching valid transitions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error fetching transitions"
        )

@router.get("/asset/{asset_id}/history", response_model=List[AssetStatusHistoryResponse])
async def get_asset_status_history(
    asset_id: int,
    limit: int = Query(50, ge=1, le=200, description="Number of history records to return"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get status change history for an asset."""
    try:
        # Verify asset exists
        asset = db.query(Asset).filter(Asset.id == asset_id).first()
        if not asset:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        # Get history
        workflow_service = AssetWorkflowService(db)
        history = workflow_service.get_asset_status_history(asset_id, limit)
        
        return history
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching asset status history: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error fetching history"
        )

@router.get("/transitions", response_model=List[WorkflowStateTransitionResponse])
async def get_workflow_transitions(
    workflow_module: Optional[WorkflowModule] = Query(None, description="Filter by workflow module"),
    from_status: Optional[str] = Query(None, description="Filter by from status"),
    to_status: Optional[str] = Query(None, description="Filter by to status"),
    is_active: bool = Query(True, description="Filter by active status"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get workflow state transitions with optional filtering."""
    try:
        query = db.query(WorkflowStateTransition).filter(
            WorkflowStateTransition.is_active == is_active
        )
        
        if workflow_module:
            query = query.filter(WorkflowStateTransition.workflow_module == workflow_module)
        if from_status:
            query = query.filter(WorkflowStateTransition.from_status == from_status)
        if to_status:
            query = query.filter(WorkflowStateTransition.to_status == to_status)
        
        transitions = query.order_by(
            WorkflowStateTransition.workflow_module,
            WorkflowStateTransition.priority.desc()
        ).all()
        
        return transitions
        
    except Exception as e:
        logger.error(f"Error fetching workflow transitions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error fetching transitions"
        )

@router.get("/assets/by-status", response_model=dict)
async def get_assets_by_status_for_workflow(
    status: str = Query(..., description="Asset status to filter by"),
    workflow_module: WorkflowModule = Query(..., description="Workflow module context"),
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get assets with specific status that can be processed by a workflow module.
    Useful for populating dropdowns in workflow UIs.
    """
    try:
        # Calculate offset
        offset = (page - 1) * limit
        
        # Get assets with the specified status
        workflow_service = AssetWorkflowService(db)
        query = db.query(Asset).filter(Asset.status == status)
        
        # Get total count
        total_count = query.count()
        
        # Apply pagination
        assets = query.order_by(Asset.asset_id).offset(offset).limit(limit).all()
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1
        
        return {
            "assets": [asset.to_dict() for asset in assets],
            "status": status.value,
            "workflow_module": workflow_module.value,
            "pagination": {
                "page": page,
                "limit": limit,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": has_next,
                "has_prev": has_prev
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching assets by status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error fetching assets"
        )

@router.get("/stats/status-summary", response_model=dict)
async def get_status_summary(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get summary of assets by status."""
    try:
        status_counts = {}
        
        # Count assets by status - get actual status values from database
        from sqlalchemy import func
        status_results = db.query(Asset.status, func.count(Asset.id)).group_by(Asset.status).all()
        for status, count in status_results:
            status_counts[status] = count
        
        total_assets = db.query(Asset).count()
        
        return {
            "total_assets": total_assets,
            "status_counts": status_counts,
            "generated_at": datetime.utcnow()
        }
        
    except Exception as e:
        logger.error(f"Error fetching status summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error fetching summary"
        )

@router.post("/transitions/{transition_id}/toggle", response_model=WorkflowStateTransitionResponse)
async def toggle_transition_status(
    transition_id: int,
    current_user: User = Depends(require_admin),
    db: Session = Depends(get_db)
):
    """Toggle the active status of a workflow transition (admin only)."""
    try:
        transition = db.query(WorkflowStateTransition).filter(
            WorkflowStateTransition.id == transition_id
        ).first()
        
        if not transition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Workflow transition not found"
            )
        
        # Toggle active status
        transition.is_active = not transition.is_active
        transition.updated_by = str(current_user.id)
        
        db.commit()
        db.refresh(transition)
        
        logger.info(f"Workflow transition {transition_id} {'activated' if transition.is_active else 'deactivated'} by admin {current_user.id}")
        return transition
        
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error toggling transition status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error toggling transition"
        )
