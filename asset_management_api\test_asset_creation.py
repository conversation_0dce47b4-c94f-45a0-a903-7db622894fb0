#!/usr/bin/env python3
"""
Test asset creation with flexible VARCHAR status and condition fields
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import requests
import json

def test_asset_creation():
    """Test creating an asset with custom status and condition values."""
    
    base_url = "http://localhost:8000"
    
    # Test data with custom status and condition values
    test_asset = {
        "asset_id": "TEST001",
        "type": "Test Equipment",
        "category": "Testing",
        "model": "Test Model",
        "serial_number": "TEST123456",
        "status": "Custom Status Value",  # Custom status - not restricted to enum
        "condition": "Custom Condition",  # Custom condition - not restricted to enum
        "location": "Test Location",
        "assigned_to": "Test User",
        "state": "SC",
        "county": "Test County",
        "precinct": "Test Precinct",
        "notes": "This is a test asset with custom status and condition values"
    }
    
    try:
        print("🧪 Testing asset creation with custom status and condition values...")
        print(f"📊 Test data: {json.dumps(test_asset, indent=2)}")
        
        # Test asset creation
        response = requests.post(f"{base_url}/api/assets", json=test_asset)
        
        print(f"📡 Response status: {response.status_code}")
        print(f"📄 Response body: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Asset creation successful!")
                print(f"✅ Created asset with custom status: '{test_asset['status']}'")
                print(f"✅ Created asset with custom condition: '{test_asset['condition']}'")
                print("🎉 VARCHAR fields are working correctly - users can enter any values!")
                return True
            else:
                print(f"❌ Asset creation failed: {result.get('message', 'Unknown error')}")
                return False
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure the FastAPI server is running on port 8000.")
        print("💡 Start the server with: python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload")
        return False
    except Exception as e:
        print(f"❌ Error testing asset creation: {e}")
        return False

def test_common_values_endpoint():
    """Test the new common values endpoint."""
    
    base_url = "http://localhost:8000"
    
    try:
        print("\n🧪 Testing common values endpoint...")
        
        response = requests.get(f"{base_url}/api/assets/common-values")
        
        print(f"📡 Response status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Common values endpoint working!")
            print(f"📋 Available status suggestions: {result.get('statuses', [])}")
            print(f"📋 Available condition suggestions: {result.get('conditions', [])}")
            print(f"🎯 Default values: {result.get('defaults', {})}")
            return True
        else:
            print(f"❌ HTTP Error {response.status_code}: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server.")
        return False
    except Exception as e:
        print(f"❌ Error testing common values endpoint: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Testing flexible VARCHAR status and condition fields...")
    print("=" * 60)
    
    # Test asset creation
    creation_success = test_asset_creation()
    
    # Test common values endpoint
    common_values_success = test_common_values_endpoint()
    
    print("\n" + "=" * 60)
    if creation_success and common_values_success:
        print("🎉 All tests passed! The system now supports flexible status and condition values!")
        print("✅ Users can enter any custom status and condition values")
        print("✅ No more enum restrictions")
        print("✅ Database uses VARCHAR(50) for maximum flexibility")
    else:
        print("❌ Some tests failed. Check the server logs for more details.")
