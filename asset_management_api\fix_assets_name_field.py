#!/usr/bin/env python3
"""
Fix Assets Table Schema - Remove name field requirement or add default
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_assets_name_field():
    """Fix the assets table name field issue."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.begin() as conn:
            logger.info("🔍 Checking current assets table structure...")
            
            # Get table structure
            result = conn.execute(text("DESCRIBE assets"))
            columns = result.fetchall()
            
            print("Current assets table columns:")
            for column in columns:
                print(f"  {column[0]}: {column[1]} {column[2]} {column[3]} {column[4]} {column[5]}")
            
            # Check if name field exists
            name_field_exists = any(col[0] == 'name' for col in columns)
            
            if name_field_exists:
                logger.info("📝 Name field exists. Making it nullable or adding default...")
                # Option 1: Make name field nullable
                try:
                    conn.execute(text("ALTER TABLE assets MODIFY COLUMN name VARCHAR(255) NULL"))
                    logger.info("✅ Made name field nullable")
                except Exception as e:
                    logger.info(f"Could not make nullable: {e}")
                    # Option 2: Add a default value
                    try:
                        conn.execute(text("ALTER TABLE assets MODIFY COLUMN name VARCHAR(255) DEFAULT 'Unnamed Asset'"))
                        logger.info("✅ Added default value to name field")
                    except Exception as e2:
                        logger.error(f"Could not add default: {e2}")
                        # Option 3: Drop the name column if it's not needed
                        try:
                            conn.execute(text("ALTER TABLE assets DROP COLUMN name"))
                            logger.info("✅ Dropped name column")
                        except Exception as e3:
                            logger.error(f"Could not drop column: {e3}")
            else:
                logger.info("ℹ️  Name field does not exist - this should be fine")
                
            # Show updated structure
            result = conn.execute(text("DESCRIBE assets"))
            columns = result.fetchall()
            
            print("\nUpdated assets table columns:")
            for column in columns:
                print(f"  {column[0]}: {column[1]} {column[2]} {column[3]} {column[4]} {column[5]}")
                
    except Exception as e:
        logger.error(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_assets_name_field()
