# app/models/assets.py
from sqlalchemy import Column, Integer, String, DateTime, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.config.database import Base
import json
from typing import Optional, Dict, Any

# Default values for status and condition (no enum restrictions)
class AssetDefaults:
    DEFAULT_STATUS = "New"
    DEFAULT_CONDITION = "Good"

    # Common status values for reference (not enforced)
    COMMON_STATUSES = [
        "New", "Ready", "Failed", "Packed", "Checked-out",
        "In-transfer", "Delivered", "Using", "Damaged",
        "Under Maintenance", "Completed", "Retired"
    ]

    # Common condition values for reference (not enforced)
    COMMON_CONDITIONS = [
        "Excellent", "Good", "Fair", "Poor", "Damaged"
    ]

class Asset(Base):
    __tablename__ = "assets"

    id = Column(Integer, primary_key=True, autoincrement=True)
    asset_id = Column(String(100), nullable=False, unique=True, index=True)
    type = Column(String(255), nullable=False)
    category = Column(String(255), nullable=True)
    model = Column(String(255), nullable=True)
    serial_number = Column(String(255), nullable=True)
    status = Column(String(50), nullable=False, default=AssetDefaults.DEFAULT_STATUS)
    condition = Column(String(50), nullable=False, default=AssetDefaults.DEFAULT_CONDITION)
    location = Column(String(255), nullable=False)
    assigned_to = Column(String(255), nullable=True)

    # Geographical hierarchy for RBAC (optional for now - will be added later)
    # state = Column(String(100), nullable=True, index=True)  # Will be added in migration
    county = Column(String(100), nullable=True, index=True)  # Optional for now
    precinct = Column(String(100), nullable=True, index=True)  # Optional, for precinct-level assets
    
    # Dates
    purchase_date = Column(DateTime, nullable=True)
    warranty_expiry = Column(DateTime, nullable=True)
    last_maintenance = Column(DateTime, nullable=True)
    next_maintenance = Column(DateTime, nullable=True)
    last_checked = Column(DateTime, nullable=True)
    
    # Additional metadata
    notes = Column(Text, nullable=True)
    specifications = Column(Text, nullable=True)  # JSON string for technical specs
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # Audit log relationships
    scan_logs = relationship("AssetScanLog", back_populates="asset", cascade="all, delete-orphan")

    def get_specifications_data(self) -> Dict[str, Any]:
        """Get specifications as dictionary."""
        try:
            return json.loads(self.specifications) if self.specifications else {}
        except (json.JSONDecodeError, TypeError):
            return {}

    def set_specifications_data(self, specs: Dict[str, Any]):
        """Set specifications from dictionary."""
        try:
            self.specifications = json.dumps(specs) if specs else None
        except (TypeError, ValueError):
            self.specifications = None

    def to_dict(self) -> dict:
        """Convert asset object to dictionary."""
        return {
            "id": self.id,
            "asset_id": self.asset_id,
            "type": self.type,
            "category": self.category,
            "model": self.model,
            "serial_number": self.serial_number,
            "status": self.status,
            "condition": self.condition,
            "location": self.location,
            "assigned_to": self.assigned_to,
            "county": self.county,
            "precinct": self.precinct,
            "purchase_date": self.purchase_date,
            "warranty_expiry": self.warranty_expiry,
            "last_maintenance": self.last_maintenance,
            "next_maintenance": self.next_maintenance,
            "last_checked": self.last_checked,
            "notes": self.notes,
            "specifications": self.get_specifications_data(),
            "created_at": self.created_at,
            "updated_at": self.updated_at
        }

    @staticmethod
    def generate_asset_id(asset_type: str, count: int) -> str:
        """Generate unique asset ID in ASS#### format."""
        # Use ASS prefix for all assets with 4-digit number
        next_number = str(count + 1).zfill(4)
        return f"ASS{next_number}"