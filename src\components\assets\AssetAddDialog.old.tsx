import { useState, useEffect } from "react";
import { Dialog, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calendar } from "lucide-react";
import { assetService } from "@/services/assetService";
import { useToast } from "@/components/ui/use-toast";

interface AssetAddDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAssetAdded?: () => void;
}

export function AssetAddDialog({ open, onOpenChange, onAssetAdded }: AssetAddDialogProps) {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const [formData, setFormData] = useState({
    asset_id: "A0010",
    serial_number: "",
    type: "",
    model: "",
    status: "",
    condition: "GOOD",
    location: "",
    assigned_to: "",
    state: "SC",
    county: "",
    precinct: "",
    purchase_date: "",
    warranty_expiry: "",
    last_maintenance: "",
    next_maintenance: "",
    notes: ""
  });

  // Auto-generate serial number when type changes
  useEffect(() => {
    if (formData.type) {
      // Generate a random serial number based on type
      const prefix = formData.type === "BMDs" ? "EV-" :
                    formData.type === "Poll Pads" ? "EPB-" :
                    formData.type === "Tabulators" ? "DS300-" :
                    formData.type === "AV Computers" ? "DELL-" :
                    formData.type === "Ballot Box (ICP)" ? "BB-" : "SN-";

      const randomNum = Math.floor(10000 + Math.random() * 90000); // 5-digit random number
      const serial_number = `${prefix}${randomNum}`;

      setFormData(prev => ({
        ...prev,
        serial_number
      }));
    }
  }, [formData.type, formData.model]);

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    setFormData({
      ...formData,
      [field]: value
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.type || !formData.location) {
      toast({
        title: "Validation Error",
        description: "Asset type and location are required fields",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    
    try {
      // Prepare the data for API call
      const assetData = {
        asset_id: formData.asset_id,
        type: formData.type,
        model: formData.model || undefined,
        serial_number: formData.serial_number || undefined,
        status: formData.status || "NEW",
        condition: formData.condition || "GOOD",
        location: formData.location,
        assigned_to: formData.assigned_to || undefined,
        state: formData.state || undefined,
        county: formData.county || undefined,
        precinct: formData.precinct || undefined,
        purchase_date: formData.purchase_date || undefined,
        warranty_expiry: formData.warranty_expiry || undefined,
        last_maintenance: formData.last_maintenance || undefined,
        next_maintenance: formData.next_maintenance || undefined,
        notes: formData.notes || undefined,
      };

      console.log("Submitting asset data:", assetData);
      
      const response = await assetService.createAsset(assetData);
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Asset created successfully",
        });
        
        if (onAssetAdded) {
          onAssetAdded();
        }
        
        onOpenChange(false);
        
        // Reset form
        setFormData({
          asset_id: "A0010",
          serial_number: "",
          type: "",
          model: "",
          status: "",
          condition: "GOOD",
          location: "",
          assigned_to: "",
          state: "SC",
          county: "",
          precinct: "",
          purchase_date: "",
          warranty_expiry: "",
          last_maintenance: "",
          next_maintenance: "",
          notes: ""
        });
      } else {
        toast({
          title: "Error",
          description: response.message || "Failed to create asset",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error creating asset:", error);
      toast({
        title: "Error",
        description: "Failed to create asset. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl p-0 overflow-hidden">
        <div className="flex justify-between items-center p-4 border-b">
          <h2 className="text-xl font-semibold">Assets</h2>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="sm" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button size="sm" onClick={handleSubmit}>
              Save
            </Button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[80vh]">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-2 gap-x-6 gap-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Asset Tag *
                </label>
                <Input
                  value={formData.tag}
                  onChange={(e) => handleInputChange("tag", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Serial
                </label>
                <Input
                  value={formData.serialNo}
                  onChange={(e) => handleInputChange("serialNo", e.target.value)}
                  readOnly
                  className="bg-gray-50"
                />
                <p className="text-xs text-muted-foreground">Auto-generated based on category</p>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Category *
                </label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => handleInputChange("category", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Ballot Box (ICP)">Ballot Box (ICP)</SelectItem>
                    <SelectItem value="AV Computers">AV Computers</SelectItem>
                    <SelectItem value="BMDs">BMDs</SelectItem>
                    <SelectItem value="Poll Pads">Poll Pads</SelectItem>
                    <SelectItem value="Tabulators">Tabulators</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Model
                </label>
                <Select
                  value={formData.model}
                  onValueChange={(value) => handleInputChange("model", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Dell Latitude 3410">Dell Latitude 3410</SelectItem>
                    <SelectItem value="Ballot Box (ICP) - 001">Ballot Box (ICP) - 001</SelectItem>
                    <SelectItem value="Acer-V206HQL">Acer-V206HQL</SelectItem>
                    <SelectItem value="BMD">BMD</SelectItem>
                    <SelectItem value="ViewSonic VA2055SM">ViewSonic VA2055SM</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Owned By *
                </label>
                <Select
                  value={formData.ownedBy}
                  onValueChange={(value) => handleInputChange("ownedBy", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="County">County</SelectItem>
                    <SelectItem value="State">State</SelectItem>
                    <SelectItem value="Federal">Federal</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Status *
                </label>
                <Select
                  value={formData.status}
                  onValueChange={(value) => handleInputChange("status", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Ready">Ready</SelectItem>
                    <SelectItem value="New">New</SelectItem>
                    <SelectItem value="Issued">Issued</SelectItem>
                    <SelectItem value="Maintenance">Maintenance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Asset Name *
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Purchase Date
                </label>
                <div className="relative">
                  <Input
                    type="text"
                    value={formData.purchaseDate}
                    onChange={(e) => handleInputChange("purchaseDate", e.target.value)}
                    placeholder="MM/DD/YYYY"
                  />
                  <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Vendor
                </label>
                <Select
                  value={formData.vendor}
                  onValueChange={(value) => handleInputChange("vendor", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Vendor 1">Vendor 1</SelectItem>
                    <SelectItem value="Vendor 2">Vendor 2</SelectItem>
                    <SelectItem value="Vendor 3">Vendor 3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Order Number
                </label>
                <Input
                  value={formData.orderNo}
                  onChange={(e) => handleInputChange("orderNo", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Purchase Cost
                </label>
                <Input
                  value={formData.purchaseCost}
                  onChange={(e) => handleInputChange("purchaseCost", e.target.value)}
                />
              </div>
              <div className="space-y-2 col-span-2">
                <label className="text-sm font-medium">
                  Warranty & Maintenance
                </label>
                <div className="grid grid-cols-2 gap-4 border rounded-md p-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Warranty Duration
                    </label>
                    <div className="flex items-center">
                      <Input
                        type="number"
                        min="0"
                        value={formData.warranty}
                        onChange={(e) => handleInputChange("warranty", e.target.value)}
                        className="flex-1"
                      />
                      <span className="ml-2 text-sm text-gray-500">months</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Warranty Type
                    </label>
                    <Select
                      value={formData.warrantyType}
                      onValueChange={(value) => handleInputChange("warrantyType", value)}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="manufacturer">Manufacturer</SelectItem>
                        <SelectItem value="extended">Extended</SelectItem>
                        <SelectItem value="service_contract">Service Contract</SelectItem>
                        <SelectItem value="maintenance_agreement">Maintenance Agreement</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Warranty Provider
                    </label>
                    <Input
                      value={formData.warrantyProvider}
                      onChange={(e) => handleInputChange("warrantyProvider", e.target.value)}
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Warranty Details
                    </label>
                    <Input
                      value={formData.warrantyDetails}
                      onChange={(e) => handleInputChange("warrantyDetails", e.target.value)}
                      placeholder="Coverage details"
                    />
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Maintenance Schedule
                    </label>
                    <div className="flex items-center">
                      <Input
                        type="number"
                        min="0"
                        value={formData.maintenanceSchedule}
                        onChange={(e) => handleInputChange("maintenanceSchedule", e.target.value)}
                        className="flex-1"
                      />
                      <span className="ml-2 text-sm text-gray-500">months</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label className="text-sm font-medium">
                      Next Maintenance Date
                    </label>
                    <div className="relative">
                      <Input
                        type="text"
                        value={formData.nextMaintenanceDate}
                        onChange={(e) => handleInputChange("nextMaintenanceDate", e.target.value)}
                        placeholder="MM/DD/YYYY"
                      />
                      <Calendar className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Default Location *
                </label>
                <Select
                  value={formData.defaultLocation}
                  onValueChange={(value) => handleInputChange("defaultLocation", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select an option" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Precinct 001 - Main office, 001, Duluth, GA, 30097">Precinct 001 - Main office, 001, Duluth, GA, 30097</SelectItem>
                    <SelectItem value="Precinct 002 - City Hall, 002, Johns Creek, GA, 30022">Precinct 002 - City Hall, 002, Johns Creek, GA, 30022</SelectItem>
                    <SelectItem value="Precinct 003 - Community Center, 003, Alpharetta, GA, 30009">Precinct 003 - Community Center, 003, Alpharetta, GA, 30009</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Building
                </label>
                <Input
                  value={formData.building}
                  onChange={(e) => handleInputChange("building", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Floor
                </label>
                <Input
                  value={formData.floor}
                  onChange={(e) => handleInputChange("floor", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Bin
                </label>
                <Input
                  value={formData.bin}
                  onChange={(e) => handleInputChange("bin", e.target.value)}
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Image
                </label>
                <div className="flex items-center gap-2">
                  <Input
                    type="file"
                    id="image-upload"
                    className="hidden"
                    onChange={(e) => {
                      if (e.target.files && e.target.files.length > 0) {
                        // In a real app, you would handle file upload here
                        console.log("File selected:", e.target.files[0].name);
                      }
                    }}
                  />
                  <Input
                    value="Choose Picture"
                    readOnly
                    className="flex-1 cursor-pointer"
                    onClick={() => document.getElementById("image-upload")?.click()}
                  />
                  <Button type="button" variant="outline">
                    Browse
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Notes
                </label>
                <Textarea
                  value={formData.notes}
                  onChange={(e) => handleInputChange("notes", e.target.value)}
                  className="h-24 resize-none"
                />
              </div>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
}
