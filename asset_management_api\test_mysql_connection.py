#!/usr/bin/env python3
"""
Test MySQL database connectivity
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mysql_connection():
    """Test if we can connect to MySQL database"""
    try:
        logger.info(f"🔍 Testing connection to: {DATABASE_URL}")
        
        # Create engine with shorter timeout
        engine = create_engine(DATABASE_URL, connect_args={"connect_timeout": 5})
        
        # Test connection
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            logger.info("✅ Successfully connected to MySQL database!")
            
            # Check if assets table exists
            result = conn.execute(text("SHOW TABLES LIKE 'assets'"))
            if result.fetchone():
                logger.info("✅ Assets table exists!")
                
                # Count records
                result = conn.execute(text("SELECT COUNT(*) FROM assets"))
                count = result.fetchone()[0]
                logger.info(f"📊 Total assets: {count}")
                
                if count > 0:
                    # Check sample data
                    result = conn.execute(text("SELECT asset_id, status, `condition` FROM assets LIMIT 3"))
                    for row in result.fetchall():
                        logger.info(f"📝 Sample: {row[0]} - Status: {row[1]} - Condition: {row[2]}")
                
                return True
            else:
                logger.warning("⚠️ Assets table does not exist!")
                return False
                
    except Exception as e:
        logger.error(f"❌ Failed to connect to MySQL database: {e}")
        return False

if __name__ == "__main__":
    test_mysql_connection()
