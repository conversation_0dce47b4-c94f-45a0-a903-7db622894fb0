
import React from "react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import {
  BarChart3,
  CheckCircle2,
  Lock,
  Shield,
  ArrowRight,
  Calendar,
  FileText,
  Users,
  Truck,
  Settings,
  Package
} from "lucide-react";
import { AspectRatio } from "@/components/ui/aspect-ratio";

const LandingPage = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <header className="relative bg-gradient-to-br from-zenith-blue-dark via-blue-700 to-blue-800 text-white overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full"
               style={{ backgroundImage: 'url("data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.4"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")', backgroundSize: '60px 60px' }}></div>
        </div>

        <div className="container mx-auto px-6 py-20 md:py-28 relative z-10">
          <div className="flex flex-col items-center md:flex-row md:justify-between">
            <div className="md:w-1/2 mb-10 md:mb-0">
              <div className="flex items-center mb-6">
                <img src="/logo.jpg" alt="SC Election Logo" className="h-16 mr-4" />
                <div className="bg-white/20 px-4 py-1 rounded-full text-sm font-medium">
                  Official Election Asset Management
                </div>
              </div>
              <h1 className="text-4xl md:text-5xl font-bold mb-6 leading-tight">
                <span className="text-blue-200">Asset Management System</span>
              </h1>
              <p className="text-lg md:text-xl mb-8 text-blue-100 max-w-xl">
                Streamlined tracking and management for election equipment across all counties with real-time monitoring and comprehensive reporting
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                <Link to="/login">
                  <Button size="lg" className="w-full sm:w-auto bg-white text-blue-700 hover:bg-blue-50">
                    Login to Dashboard
                  </Button>
                </Link>
                <Button variant="outline" size="lg" className="w-full sm:w-auto bg-white/10 text-white border-white/20 hover:bg-white/20 group">
                  Learn More
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Button>
              </div>
            </div>
            <div className="md:w-5/12">
              <div className="bg-white/10 rounded-xl p-5 backdrop-blur-sm border border-white/20 shadow-2xl transition-transform duration-500">
                <AspectRatio ratio={16/9} className="bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-lg overflow-hidden">
                  <div className="h-full w-full flex items-center justify-center p-8">
                    <div className="grid grid-cols-2 gap-4 w-full">
                      <div className="bg-white/20 rounded-lg p-4 flex flex-col items-center justify-center text-center">
                        <BarChart3 className="h-10 w-10 text-white mb-2" />
                        <span className="text-sm font-medium">Asset Analytics</span>
                      </div>
                      <div className="bg-white/20 rounded-lg p-4 flex flex-col items-center justify-center text-center">
                        <Calendar className="h-10 w-10 text-white mb-2" />
                        <span className="text-sm font-medium">Election Planning</span>
                      </div>
                      <div className="bg-white/20 rounded-lg p-4 flex flex-col items-center justify-center text-center">
                        <FileText className="h-10 w-10 text-white mb-2" />
                        <span className="text-sm font-medium">Compliance</span>
                      </div>
                      <div className="bg-white/20 rounded-lg p-4 flex flex-col items-center justify-center text-center">
                        <Settings className="h-10 w-10 text-white mb-2" />
                        <span className="text-sm font-medium">Maintenance</span>
                      </div>
                    </div>
                  </div>
                </AspectRatio>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Features Section */}
      <section className="py-20 container mx-auto px-6">
        <div className="text-center max-w-3xl mx-auto mb-16">
          <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
            Comprehensive Asset Management Solution
          </h2>
          <p className="text-lg text-gray-600">
            Our platform provides all the tools election officials need to efficiently manage, track, and maintain voting equipment
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="h-1 bg-blue-500 w-full"></div>
            <CardContent className="pt-8 pb-8">
              <div className="mb-6 bg-blue-100 p-4 rounded-full w-16 h-16 flex items-center justify-center group-hover:scale-110 transition-transform">
                <BarChart3 className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Real-Time Monitoring</h3>
              <p className="text-gray-600 leading-relaxed">
                Track the status and location of all election assets across counties with real-time updates and comprehensive dashboards.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/login" className="text-blue-600 font-medium flex items-center hover:text-blue-700">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="h-1 bg-green-500 w-full"></div>
            <CardContent className="pt-8 pb-8">
              <div className="mb-6 bg-green-100 p-4 rounded-full w-16 h-16 flex items-center justify-center group-hover:scale-110 transition-transform">
                <CheckCircle2 className="h-8 w-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Maintenance Management</h3>
              <p className="text-gray-600 leading-relaxed">
                Schedule and track maintenance activities to ensure all equipment is in optimal condition for election day readiness.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/login" className="text-green-600 font-medium flex items-center hover:text-green-700">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="h-1 bg-amber-500 w-full"></div>
            <CardContent className="pt-8 pb-8">
              <div className="mb-6 bg-amber-100 p-4 rounded-full w-16 h-16 flex items-center justify-center group-hover:scale-110 transition-transform">
                <Shield className="h-8 w-8 text-amber-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Compliance & Security</h3>
              <p className="text-gray-600 leading-relaxed">
                Ensure all assets comply with regulations and maintain the highest security standards with detailed audit trails.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/login" className="text-amber-600 font-medium flex items-center hover:text-amber-700">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-12">
          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="h-1 bg-purple-500 w-full"></div>
            <CardContent className="pt-8 pb-8">
              <div className="mb-6 bg-purple-100 p-4 rounded-full w-16 h-16 flex items-center justify-center group-hover:scale-110 transition-transform">
                <Truck className="h-8 w-8 text-purple-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Vendor Management</h3>
              <p className="text-gray-600 leading-relaxed">
                Manage relationships with equipment vendors, track warranties, and streamline the procurement process for election supplies.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/login" className="text-purple-600 font-medium flex items-center hover:text-purple-700">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className="border-0 shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden group">
            <div className="h-1 bg-indigo-500 w-full"></div>
            <CardContent className="pt-8 pb-8">
              <div className="mb-6 bg-indigo-100 p-4 rounded-full w-16 h-16 flex items-center justify-center group-hover:scale-110 transition-transform">
                <Package className="h-8 w-8 text-indigo-600" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Inventory Control</h3>
              <p className="text-gray-600 leading-relaxed">
                Maintain accurate inventory of all election assets, consumables, and supplies with barcode scanning and automated alerts.
              </p>
              <div className="mt-6 pt-4 border-t border-gray-100">
                <Link to="/login" className="text-indigo-600 font-medium flex items-center hover:text-indigo-700">
                  Learn more <ArrowRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-gradient-to-b from-gray-50 to-gray-100 py-24 relative overflow-hidden">
        <div className="absolute inset-0 opacity-5">
          <svg className="h-full w-full" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32">
            <path d="M0 4h4v4H0V4zm8 0h4v4H8V4zm8 0h4v4h-4V4zm8 0h4v4h-4V4zM4 8h4v4H4V8zm8 0h4v4h-4V8zm8 0h4v4h-4V8zM0 16h4v4H0v-4zm8 0h4v4H8v-4zm8 0h4v4h-4v-4zm8 0h4v4h-4v-4zM4 20h4v4H4v-4zm8 0h4v4h-4v-4zm8 0h4v4h-4v-4zM0 28h4v4H0v-4zm8 0h4v4H8v-4zm8 0h4v4h-4v-4zm8 0h4v4h-4v-4z" fill="currentColor" />
          </svg>
        </div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center max-w-3xl mx-auto mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 text-gray-800">
              Statewide Asset Overview
            </h2>
            <p className="text-lg text-gray-600">
              Managing thousands of election assets across South Carolina with precision and accountability
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="bg-white p-10 rounded-xl shadow-xl transform hover:-translate-y-2 transition-transform duration-300">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-blue-100 rounded-full mb-6">
                <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
              </div>
              <div className="text-5xl font-bold text-blue-600 mb-3">5,600+</div>
              <div className="text-gray-700 font-medium text-lg">Electronic Poll Books</div>
              <div className="mt-4 text-sm text-gray-500">Used for voter check-in and verification</div>
            </div>

            <div className="bg-white p-10 rounded-xl shadow-xl transform hover:-translate-y-2 transition-transform duration-300">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
                <svg className="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"></path>
                </svg>
              </div>
              <div className="text-5xl font-bold text-green-600 mb-3">3,200+</div>
              <div className="text-gray-700 font-medium text-lg">Ballot Scanners</div>
              <div className="mt-4 text-sm text-gray-500">For accurate and secure vote counting</div>
            </div>

            <div className="bg-white p-10 rounded-xl shadow-xl transform hover:-translate-y-2 transition-transform duration-300">
              <div className="inline-flex items-center justify-center w-20 h-20 bg-purple-100 rounded-full mb-6">
                <svg className="w-10 h-10 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                </svg>
              </div>
              <div className="text-5xl font-bold text-purple-600 mb-3">16,000+</div>
              <div className="text-gray-700 font-medium text-lg">Ballot Marking Devices</div>
              <div className="mt-4 text-sm text-gray-500">Ensuring accessible voting for all citizens</div>
            </div>
          </div>

          <div className="mt-16 text-center">
            <div className="inline-block bg-white/80 backdrop-blur-sm px-6 py-3 rounded-full text-gray-600 shadow-md">
              <span className="font-medium">Serving all 46 counties</span> • <span>Over 3.3 million registered voters</span> • <span>2,500+ polling locations</span>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative py-24 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-blue-100 to-blue-50 opacity-70"></div>
        <div className="absolute right-0 bottom-0 opacity-20">
          <svg width="400" height="400" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
            <path fill="#3B82F6" d="M39.9,-65.7C52.8,-59.6,65.2,-50.8,72.1,-38.5C79,-26.2,80.3,-10.5,79.1,4.7C77.9,19.9,74.1,34.6,65.4,45.9C56.7,57.2,43.1,65.1,28.8,70.3C14.6,75.5,-0.3,78,-14.9,75.8C-29.5,73.6,-43.8,66.7,-54.5,56.2C-65.2,45.7,-72.3,31.5,-76.3,16.2C-80.3,0.9,-81.2,-15.5,-76.1,-29.8C-71,-44.1,-60,-56.3,-46.5,-62.2C-33,-68.1,-16.5,-67.7,-1.2,-65.8C14.1,-63.9,28.1,-60.5,39.9,-65.7Z" transform="translate(100 100)" />
          </svg>
        </div>

        <div className="container mx-auto px-6 relative z-10">
          <div className="max-w-4xl mx-auto bg-white rounded-2xl shadow-2xl overflow-hidden">
            <div className="flex flex-col md:flex-row">
              <div className="md:w-1/2 bg-gradient-to-br from-blue-600 to-blue-800 p-12 text-white">
                <h3 className="text-2xl font-bold mb-4">Why Choose Our Platform?</h3>
                <ul className="space-y-4">
                  <li className="flex items-start">
                    <CheckCircle2 className="h-6 w-6 mr-2 flex-shrink-0 text-blue-200" />
                    <span>Comprehensive asset tracking and management</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-6 w-6 mr-2 flex-shrink-0 text-blue-200" />
                    <span>Real-time status updates and notifications</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-6 w-6 mr-2 flex-shrink-0 text-blue-200" />
                    <span>Detailed maintenance and service records</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-6 w-6 mr-2 flex-shrink-0 text-blue-200" />
                    <span>Secure, role-based access controls</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle2 className="h-6 w-6 mr-2 flex-shrink-0 text-blue-200" />
                    <span>Advanced reporting and analytics</span>
                  </li>
                </ul>
              </div>
              <div className="md:w-1/2 p-12">
                <h2 className="text-3xl font-bold mb-6 text-gray-800">
                  Ready to manage your election assets effectively?
                </h2>
                <p className="text-gray-600 mb-8">
                  Our comprehensive asset management system provides all the tools you need to ensure election equipment is properly tracked, maintained, and ready when needed.
                </p>
                <Link to="/login">
                  <Button size="lg" className="w-full bg-blue-600 hover:bg-blue-700">
                    Login to System
                  </Button>
                </Link>
                <div className="mt-6 text-center text-gray-500 text-sm">
                  Need assistance? Contact our support team at <span className="font-medium"><EMAIL></span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-12">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center mb-6">
                <img src="/logo.jpg" alt="SC Election Logo" className="h-12 mr-4" />
                <div>
                  <h3 className="text-xl font-bold">Asset Management</h3>
                  <p className="text-blue-300 mt-1">Secure. Reliable. Accountable.</p>
                </div>
              </div>
              <p className="text-gray-400 mb-6 max-w-md">
                The official asset management platform for election's equipment, ensuring transparency and efficiency in the electoral process.
              </p>
              <div className="flex items-center space-x-4">
                <div className="flex items-center bg-gray-800 px-4 py-2 rounded-full">
                  <Lock className="h-5 w-5 text-blue-300 mr-2" />
                  <span className="text-gray-300 text-sm">Secure Platform</span>
                </div>
                <div className="flex items-center bg-gray-800 px-4 py-2 rounded-full">
                  <Shield className="h-5 w-5 text-blue-300 mr-2" />
                  <span className="text-gray-300 text-sm">NIST Compliant</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4 text-white">Quick Links</h4>
              <ul className="space-y-3">
                <li><Link to="/login" className="text-gray-400 hover:text-white transition-colors">Login</Link></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">About Us</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Contact</a></li>
                <li><a href="#" className="text-gray-400 hover:text-white transition-colors">Support</a></li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4 text-white">Contact Information</h4>
              <ul className="space-y-3 text-gray-400">
                <li className="flex items-start">
                  <span className="mr-2">📍</span>
                  <span>130 Technology Parkway
Peachtree Corners
GA 30092</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">📞</span>
                  <span>+1 716-767-VOTE</span>
                </li>
                <li className="flex items-start">
                  <span className="mr-2">✉️</span>
                  <span><EMAIL>

</span>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-500 text-sm mb-4 md:mb-0">
              &copy; {new Date().getFullYear()} BallotDA Platform Developed by Sonline LLC. All rights reserved.
            </div>
            <div className="flex space-x-6">
              <a href="#" className="text-gray-500 hover:text-white transition-colors">Privacy Policy</a>
              <a href="#" className="text-gray-500 hover:text-white transition-colors">Terms of Service</a>
              <a href="#" className="text-gray-500 hover:text-white transition-colors">Accessibility</a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
