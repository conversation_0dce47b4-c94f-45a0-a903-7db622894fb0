#!/usr/bin/env python3
"""
Simple script to check assets table schema
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from app.config.database import DATABASE_URL

def check_schema():
    """Check the assets table schema."""
    
    engine = create_engine(DATABASE_URL)
    
    try:
        with engine.connect() as conn:
            # Check current table structure
            print("🔍 Checking current assets table structure...")

            result = conn.execute(text("SHOW CREATE TABLE assets"))
            table_def = result.fetchone()[1]
            print("Current assets table definition:")
            print(table_def)
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_schema()
