#!/usr/bin/env python3
"""
Fix database enum values in the actual MySQL database
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.config.database import DATABASE_URL
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_mysql_enum_values():
    """Fix enum values in the MySQL database to match Python model"""
    try:
        # Create engine
        engine = create_engine(DATABASE_URL, echo=True)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        
        with SessionLocal() as session:
            logger.info("🔍 Checking current enum values in MySQL database...")
            
            # Check if assets table exists
            result = session.execute(text("SHOW TABLES LIKE 'assets'"))
            if not result.fetchone():
                logger.error("❌ Assets table does not exist!")
                return False
            
            logger.info("✅ Assets table exists!")
            
            # Check current status values
            result = session.execute(text("SELECT DISTINCT status FROM assets WHERE status IS NOT NULL"))
            status_values = [row[0] for row in result.fetchall()]
            logger.info(f"📊 Current status values: {status_values}")
            
            # Check current condition values
            result = session.execute(text("SELECT DISTINCT `condition` FROM assets WHERE `condition` IS NOT NULL"))
            condition_values = [row[0] for row in result.fetchall()]
            logger.info(f"📊 Current condition values: {condition_values}")
            
            # Define the mapping from database values to Python enum values
            status_mapping = {
                'New': 'NEW',
                'Ready': 'READY', 
                'In Transfer': 'IN_TRANSFER',
                'Using': 'USING',
                'Under Maintenance': 'UNDER_MAINTENANCE',
                'Damaged': 'DAMAGED',
                'Retired': 'RETIRED',
                'Failed': 'FAILED'
            }
            
            condition_mapping = {
                'Excellent': 'EXCELLENT',
                'Good': 'GOOD',
                'Fair': 'FAIR', 
                'Poor': 'POOR',
                'Damaged': 'DAMAGED'
            }
            
            # Update status values
            logger.info("\\n🔄 Updating status values...")
            for old_value, new_value in status_mapping.items():
                result = session.execute(text("UPDATE assets SET status = :new_value WHERE status = :old_value"), 
                                       {"new_value": new_value, "old_value": old_value})
                affected = result.rowcount
                if affected > 0:
                    logger.info(f"   ✅ Updated {affected} records: '{old_value}' → '{new_value}'")
            
            # Update condition values  
            logger.info("\\n🔄 Updating condition values...")
            for old_value, new_value in condition_mapping.items():
                result = session.execute(text("UPDATE assets SET `condition` = :new_value WHERE `condition` = :old_value"), 
                                       {"new_value": new_value, "old_value": old_value})
                affected = result.rowcount
                if affected > 0:
                    logger.info(f"   ✅ Updated {affected} records: '{old_value}' → '{new_value}'")
            
            # Commit the changes
            session.commit()
            
            # Verify the changes
            logger.info("\\n🔍 Verifying updated values...")
            result = session.execute(text("SELECT DISTINCT status FROM assets WHERE status IS NOT NULL"))
            new_status_values = [row[0] for row in result.fetchall()]
            logger.info(f"📊 Updated status values: {new_status_values}")
            
            result = session.execute(text("SELECT DISTINCT `condition` FROM assets WHERE `condition` IS NOT NULL"))
            new_condition_values = [row[0] for row in result.fetchall()]
            logger.info(f"📊 Updated condition values: {new_condition_values}")
            
            logger.info("\\n✅ MySQL database enum values updated successfully!")
            return True
        
    except Exception as e:
        logger.error(f"❌ Error updating MySQL database enum values: {e}")
        return False

if __name__ == "__main__":
    success = fix_mysql_enum_values()
    sys.exit(0 if success else 1)
