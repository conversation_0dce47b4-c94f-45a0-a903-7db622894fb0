#!/usr/bin/env python3
"""
Check database structure and contents
"""
import sqlite3
import os

def check_database():
    """Check what tables exist in the database"""
    db_path = "asset_management.db"
    
    if not os.path.exists(db_path):
        print(f"❌ Database file {db_path} does not exist")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all tables
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("📋 Tables in database:")
        for table in tables:
            print(f"  - {table[0]}")
        
        # Check if there are any asset-related tables
        for table in tables:
            table_name = table[0]
            if 'asset' in table_name.lower():
                print(f"\\n🔍 Checking table: {table_name}")
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                for col in columns:
                    print(f"  Column: {col[1]} ({col[2]})")
                
                # Check sample data
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                print(f"  📊 Records: {count}")
                
                if count > 0:
                    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
                    rows = cursor.fetchall()
                    print("  📝 Sample data:")
                    for row in rows:
                        print(f"    {row}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error checking database: {e}")

if __name__ == "__main__":
    check_database()
