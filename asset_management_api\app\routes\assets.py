# app/routes/assets.py
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi import status as http_status
from sqlalchemy.orm import Session
from sqlalchemy import func, or_, and_
from app.config.database import get_db
from app.models.assets import Asset, AssetStatus, AssetCondition
from app.models.user import User
from app.middleware.auth import get_current_user, require_admin
from app.middleware.rbac import get_rbac_service, RBACService, require_asset_access, enforce_rbac
from app.schemas.assets import AssetCreate, AssetUpdate, AssetResponse, AssetStatusUpdate
from typing import Optional, List
import logging
from datetime import datetime

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get("/", response_model=dict)
@router.get("", response_model=dict)  # Handle both with and without trailing slash
async def get_assets(
    page: int = Query(1, ge=1),
    limit: int = Query(50, ge=1, le=200),
    search: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None, alias="type"),
    status: Optional[AssetStatus] = Query(None),
    condition: Optional[AssetCondition] = Query(None),
    location: Optional[str] = Query(None),
    county: Optional[str] = Query(None),
    precinct: Optional[str] = Query(None),
    # rbac: RBACService = Depends(get_rbac_service),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get all assets with optional filtering and pagination.
    Equivalent to GET /api/assets in Node.js version.
    """
    try:
        # Calculate offset
        offset = (page - 1) * limit
        
        # Build query with RBAC filtering
        query = db.query(Asset)

        # Apply RBAC filters first (most important for security)
        # query = rbac.apply_asset_filters(query)  # Temporarily disabled for testing

        # Apply search filters
        if search:
            search_filter = or_(
                Asset.asset_id.ilike(f"%{search}%"),
                Asset.type.ilike(f"%{search}%"),
                Asset.model.ilike(f"%{search}%"),
                Asset.serial_number.ilike(f"%{search}%"),
                Asset.location.ilike(f"%{search}%")
            )
            query = query.filter(search_filter)
        
        # Apply specific filters
        if asset_type:
            query = query.filter(Asset.type == asset_type)
        if status:
            query = query.filter(Asset.status == status)
        if condition:
            query = query.filter(Asset.condition == condition)
        if location:
            query = query.filter(Asset.location.ilike(f"%{location}%"))
        if county:
            query = query.filter(Asset.county == county)
        if precinct:
            query = query.filter(Asset.precinct == precinct)
        
        # Get total count
        total_count = query.count()
        
        # Apply ordering first, then pagination
        assets = query.order_by(Asset.created_at.desc()).offset(offset).limit(limit).all()
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        
        return {
            "success": True,
            "data": {
                "assets": [asset.to_dict() for asset in assets],
                "pagination": {
                    "page": page,
                    "limit": limit,
                    "total": total_count,
                    "totalPages": total_pages
                }
            }
        }
        
    except Exception as e:
        logger.error(f"Error fetching assets: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch assets"
        )

@router.get("/stats")
async def get_asset_stats(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get asset statistics for dashboard.
    Equivalent to GET /api/assets/stats in Node.js version.
    """
    try:
        # Basic counts
        total_assets = db.query(Asset).count()
        ready_assets = db.query(Asset).filter(Asset.status == AssetStatus.READY).count()
        in_transfer_assets = db.query(Asset).filter(Asset.status == AssetStatus.IN_TRANSFER).count()
        using_assets = db.query(Asset).filter(Asset.status == AssetStatus.USING).count()
        damaged_assets = db.query(Asset).filter(Asset.status == AssetStatus.DAMAGED).count()
        under_maintenance_assets = db.query(Asset).filter(Asset.status == AssetStatus.UNDER_MAINTENANCE).count()
        
        # Asset types breakdown
        asset_types = db.query(
            Asset.type,
            func.count(Asset.id).label('count')
        ).group_by(Asset.type).all()
        
        # Condition breakdown
        condition_stats = db.query(
            Asset.condition,
            func.count(Asset.id).label('count')
        ).group_by(Asset.condition).all()
        
        return {
            "total": total_assets,
            "ready": ready_assets,
            "inTransfer": in_transfer_assets,
            "using": using_assets,
            "damaged": damaged_assets,
            "underMaintenance": under_maintenance_assets,
            "assetTypes": [{"type": at.type, "count": at.count} for at in asset_types],
            "conditionStats": [{"condition": cs.condition, "count": cs.count} for cs in condition_stats]
        }
        
    except Exception as e:
        logger.error(f"Error fetching asset stats: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch asset statistics"
        )

@router.get("/{asset_id}", response_model=dict)
async def get_asset(
    asset_id: int,
    # rbac: RBACService = Depends(get_rbac_service),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get specific asset by ID with RBAC access control.
    Equivalent to GET /api/assets/:id in Node.js version.
    """
    try:
        asset = db.query(Asset).filter(Asset.id == asset_id).first()

        if not asset:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )

        # Check RBAC access
        # if not rbac.can_access_asset(asset):  # Temporarily disabled for testing
        #     raise HTTPException(
        #         status_code=http_status.HTTP_403_FORBIDDEN,
        #         detail="Insufficient permissions to access this asset"
        #     )

        return asset.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching asset: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch asset"
        )

@router.get("/by-asset-id/{asset_asset_id}", response_model=dict)
async def get_asset_by_asset_id(
    asset_asset_id: str,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get asset by asset_id field.
    Equivalent to GET /api/assets/by-asset-id/:assetId in Node.js version.
    """
    try:
        asset = db.query(Asset).filter(Asset.asset_id == asset_asset_id).first()
        
        if not asset:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        return asset.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error fetching asset: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch asset"
        )

@router.post("/", response_model=dict)
@router.post("", response_model=dict)  # Handle both with and without trailing slash
async def create_asset(
    asset_data: AssetCreate,
    # rbac: RBACService = Depends(get_rbac_service),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Create new asset with RBAC validation.
    Equivalent to POST /api/assets in Node.js version.
    """
    try:
        logger.info(f"Creating asset with data: {asset_data}")
        
        # Validate RBAC permissions for creating asset in specified location
        # if not rbac.can_create_asset(asset_data.model_dump()):  # Temporarily disabled for testing
        #     raise HTTPException(
        #         status_code=http_status.HTTP_403_FORBIDDEN,
        #         detail="Insufficient permissions to create asset in this location"
        #     )

        # Generate asset ID if not provided
        if not asset_data.asset_id:
            # Find the highest existing ASS#### number
            import re
            all_assets = db.query(Asset.asset_id).all()
            max_number = 0
            
            for asset in all_assets:
                if asset.asset_id:
                    match = re.match(r'ASS(\d+)', asset.asset_id)
                    if match:
                        number = int(match.group(1))
                        if number > max_number:
                            max_number = number
            
            # Generate next asset ID
            next_number = max_number + 1
            asset_data.asset_id = f"ASS{str(next_number).zfill(4)}"
            logger.info(f"Generated asset_id: {asset_data.asset_id}")

        # Check if asset ID already exists
        existing_asset = db.query(Asset).filter(Asset.asset_id == asset_data.asset_id).first()
        if existing_asset:
            raise HTTPException(
                status_code=http_status.HTTP_400_BAD_REQUEST,
                detail="Asset ID already exists"
            )
        
        # Create new asset
        new_asset = Asset(
            asset_id=asset_data.asset_id,
            type=asset_data.type,
            model=asset_data.model,
            serial_number=asset_data.serial_number,
            status=asset_data.status or AssetStatus.NEW,
            condition=asset_data.condition or AssetCondition.GOOD,
            location=asset_data.location,
            assigned_to=asset_data.assigned_to,
            county=asset_data.county,
            precinct=asset_data.precinct,
            purchase_date=asset_data.purchase_date,
            warranty_expiry=asset_data.warranty_expiry,
            last_maintenance=asset_data.last_maintenance,
            next_maintenance=asset_data.next_maintenance,
            last_checked=asset_data.last_checked,
            notes=asset_data.notes
        )
        
        logger.info(f"Created asset object: {new_asset}")
        
        # Set specifications if provided
        if asset_data.specifications:
            new_asset.set_specifications_data(asset_data.specifications)
        
        # Save to database
        db.add(new_asset)
        db.commit()
        db.refresh(new_asset)
        
        logger.info(f"Successfully created asset with ID: {new_asset.id}")
        
        return {
            "success": True,
            "data": new_asset.to_dict(),
            "message": "Asset created successfully"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating asset: {e}")
        logger.error(f"Asset data: {asset_data}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create asset: {str(e)}"
        )

@router.put("/{asset_id}", response_model=dict)
async def update_asset(
    asset_id: int,
    asset_data: AssetUpdate,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Update asset.
    Equivalent to PUT /api/assets/:id in Node.js version.
    """
    try:
        asset = db.query(Asset).filter(Asset.id == asset_id).first()
        
        if not asset:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        # Check if asset_id is being changed and if it already exists
        if asset_data.asset_id and asset_data.asset_id != asset.asset_id:
            existing_asset = db.query(Asset).filter(Asset.asset_id == asset_data.asset_id).first()
            if existing_asset:
                raise HTTPException(
                    status_code=http_status.HTTP_400_BAD_REQUEST,
                    detail="Asset ID already exists"
                )
        
        # Update fields
        update_data = asset_data.dict(exclude_unset=True)
        
        # Handle specifications separately
        if 'specifications' in update_data:
            specs = update_data.pop('specifications')
            if specs is not None:
                asset.set_specifications_data(specs)
        
        # Update other fields
        for field, value in update_data.items():
            if hasattr(asset, field):
                setattr(asset, field, value)
        
        db.commit()
        db.refresh(asset)
        
        return asset.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating asset: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update asset"
        )

@router.delete("/{asset_id}")
async def delete_asset(
    asset_id: int,
    # current_user: User = Depends(require_admin),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Delete asset.
    Equivalent to DELETE /api/assets/:id in Node.js version.
    """
    try:
        asset = db.query(Asset).filter(Asset.id == asset_id).first()
        
        if not asset:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        db.delete(asset)
        db.commit()
        
        return {"message": "Asset deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting asset: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete asset"
        )

@router.put("/{asset_id}/status", response_model=dict)
async def update_asset_status(
    asset_id: int,
    status_data: AssetStatusUpdate,
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Update asset status.
    Equivalent to PUT /api/assets/:id/status in Node.js version.
    """
    try:
        asset = db.query(Asset).filter(Asset.id == asset_id).first()
        
        if not asset:
            raise HTTPException(
                status_code=http_status.HTTP_404_NOT_FOUND,
                detail="Asset not found"
            )
        
        # Update status and related fields
        asset.status = status_data.status
        asset.last_checked = datetime.utcnow()
        
        if status_data.location:
            asset.location = status_data.location
        if status_data.assigned_to is not None:
            asset.assigned_to = status_data.assigned_to
        if status_data.notes is not None:
            asset.notes = status_data.notes
        
        db.commit()
        db.refresh(asset)
        
        return asset.to_dict()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating asset status: {e}")
        db.rollback()
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update asset status"
        )

@router.get("/locations/list", response_model=List[str])
async def get_asset_locations(
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get unique asset locations.
    Equivalent to GET /api/assets/locations/list in Node.js version.
    """
    try:
        locations = db.query(Asset.location).distinct().order_by(Asset.location).all()
        return [location[0] for location in locations if location[0]]
        
    except Exception as e:
        logger.error(f"Error fetching locations: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch locations"
        )

@router.get("/types/list", response_model=List[str])
async def get_asset_types(
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
    db: Session = Depends(get_db)
):
    """
    Get unique asset types.
    Equivalent to GET /api/assets/types/list in Node.js version.
    """
    try:
        types = db.query(Asset.type).distinct().order_by(Asset.type).all()
        return [asset_type[0] for asset_type in types if asset_type[0]]
        
    except Exception as e:
        logger.error(f"Error fetching asset types: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch asset types"
        )

@router.get("/next-id", response_model=dict)
async def get_next_asset_id(
    db: Session = Depends(get_db)
    # current_user: User = Depends(get_current_user),  # Temporarily disabled for testing
):
    """
    Get the next available asset ID in ASS#### format.
    """
    try:
        # Find the highest existing ASS#### number
        assets = db.query(Asset).filter(
            Asset.asset_id.like('ASS%')
        ).all()
        
        highest_num = 0
        for asset in assets:
            try:
                # Extract number from ASS#### format
                if asset.asset_id.startswith('ASS') and len(asset.asset_id) >= 6:
                    num_str = asset.asset_id[3:]  # Remove 'ASS' prefix
                    if num_str.isdigit():
                        num = int(num_str)
                        if num > highest_num:
                            highest_num = num
            except (ValueError, IndexError):
                continue
        
        # Generate next asset ID
        next_num = highest_num + 1
        next_asset_id = f"ASS{next_num:04d}"  # Format with leading zeros
        
        return {
            "success": True,
            "asset_id": next_asset_id
        }
        
    except Exception as e:
        logger.error(f"Error generating next asset ID: {e}")
        raise HTTPException(
            status_code=http_status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate next asset ID"
        )